# Cursor MCP 工具管理指南

## 当前状态
根据您的截图，您当前有 **54 个工具**，超过了建议的 **40 个工具** 限制。这可能会影响性能。

## 工具优化建议

### 1. 保留核心工具（推荐启用）
- ✅ **sequential-thinking-mcp** (1个工具) - 思维链推理
- ✅ **memory** (9个工具) - 知识图谱和记忆管理
- ✅ **huggingface-mcp** (新增) - Hugging Face 模型和数据集管理
- ✅ **firecrawl-mcp** (已配置) - 网页爬取和数据提取

### 2. 可选工具（根据需要启用）
- ⚠️ **filesystem-mcp** (当前禁用) - 文件系统操作
- ⚠️ **time-mcp** (当前禁用) - 时间相关功能

### 3. 建议禁用的工具
- ❌ 重复功能的工具
- ❌ 不常用的工具
- ❌ 性能影响较大的工具

## 工具数量优化策略

### 策略 1: 按需启用
```
当前工具: 54个
目标工具: 40个以内
需要减少: 14个以上
```

### 策略 2: 功能整合
- 将相似功能的工具合并
- 使用更高效的工具替代多个小工具
- 优先保留核心功能工具

### 策略 3: 动态管理
- 根据项目需要动态启用/禁用工具
- 定期清理不使用的工具
- 监控工具使用情况

## Hugging Face MCP 配置

### 安装步骤
1. 运行安装脚本：
   ```bash
   ./services/mcp-config/setup_cursor_huggingface.sh
   ```

2. 获取 API Token：
   - 访问: https://huggingface.co/settings/tokens
   - 创建新的 API Token
   - 复制 Token（以 "hf_" 开头）

3. 更新配置：
   - 打开 `.cursor/mcp.json`
   - 将 "hf_your_token_here" 替换为真实 Token

4. 重启 Cursor

### 功能特性
- 🔍 **模型搜索**: 搜索和浏览 Hugging Face 模型
- 📥 **模型下载**: 下载模型到本地
- 📊 **数据集管理**: 搜索和下载数据集
- 🤖 **推理服务**: 本地和云端模型推理

## 性能优化建议

### 1. 工具优先级
```
高优先级 (必须保留):
- sequential-thinking-mcp
- memory
- huggingface-mcp
- firecrawl-mcp

中优先级 (根据需要):
- filesystem-mcp
- time-mcp

低优先级 (可禁用):
- 其他不常用工具
```

### 2. 内存管理
- 定期清理缓存
- 监控内存使用情况
- 避免同时启用过多工具

### 3. 网络优化
- 使用稳定的网络连接
- 配置合适的超时时间
- 避免频繁的 API 调用

## 故障排除

### 常见问题
1. **工具不显示**: 重启 Cursor 并检查配置
2. **性能下降**: 减少启用的工具数量
3. **API 错误**: 检查 Token 和网络连接

### 调试步骤
1. 检查配置文件: `.cursor/mcp.json`
2. 运行测试脚本: `python3 test_cursor_huggingface.py`
3. 查看 Cursor 日志
4. 验证 API Token 有效性

## 最佳实践

### 1. 工具选择原则
- 优先选择功能强大且稳定的工具
- 避免功能重复的工具
- 定期评估工具的使用价值

### 2. 配置管理
- 备份重要配置文件
- 使用版本控制管理配置
- 定期更新工具版本

### 3. 安全考虑
- 妥善保管 API Token
- 定期轮换敏感信息
- 监控异常使用情况

## 监控和维护

### 1. 性能监控
- 监控工具响应时间
- 跟踪内存使用情况
- 记录错误和异常

### 2. 定期维护
- 更新工具版本
- 清理无用配置
- 优化工具组合

### 3. 用户反馈
- 收集使用体验
- 识别问题工具
- 持续改进配置

## 总结

通过合理管理 MCP 工具，您可以：
- ✅ 提高 Cursor 性能
- ✅ 减少资源占用
- ✅ 获得更好的用户体验
- ✅ 保持功能完整性

建议您按照本指南优化工具配置，将工具数量控制在 40 个以内，同时保留核心功能。 