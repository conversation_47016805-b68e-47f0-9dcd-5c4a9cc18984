#!/bin/bash

# Hugging Face MCP 安装脚本
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

echo "🚀 开始安装 Hugging Face MCP..."

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    echo "请访问 https://nodejs.org/ 下载并安装 Node.js"
    exit 1
fi

echo "✅ Node.js 已安装，版本: $(node --version)"

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到 npm"
    exit 1
fi

echo "✅ npm 已安装，版本: $(npm --version)"

# 安装 Hugging Face MCP 包
echo "📦 正在安装 Hugging Face MCP 包..."
if npm install -g @modelcontextprotocol/server-huggingface; then
    echo "✅ Hugging Face MCP 包安装成功"
else
    echo "❌ Hugging Face MCP 包安装失败"
    exit 1
fi

# 检查安装是否成功
if npx @modelcontextprotocol/server-huggingface --help &> /dev/null; then
    echo "✅ Hugging Face MCP 安装验证成功"
else
    echo "❌ Hugging Face MCP 安装验证失败"
    exit 1
fi

# 创建配置文件模板
echo "📝 创建配置文件模板..."

CONFIG_TEMPLATE="{
  \"mcpServers\": {
    \"huggingface-mcp\": {
      \"command\": \"npx\",
      \"args\": [\"-y\", \"@modelcontextprotocol/server-huggingface\"],
      \"env\": {
        \"HUGGINGFACE_API_KEY\": \"YOUR_HUGGINGFACE_API_TOKEN_HERE\"
      }
    }
  }
}"

echo "$CONFIG_TEMPLATE" > huggingface_mcp_config_template.json

echo "✅ 配置文件模板已创建: huggingface_mcp_config_template.json"

# 显示下一步操作指南
echo ""
echo "🎯 下一步操作:"
echo "1. 访问 https://huggingface.co/settings/tokens 获取 API Token"
echo "2. 将 Token 替换到配置文件中的 'YOUR_HUGGINGFACE_API_TOKEN_HERE'"
echo "3. 将配置添加到 Claude Desktop 的配置文件中"
echo "4. 重启 Claude Desktop 以加载新配置"
echo ""
echo "📚 详细配置指南请参考: services/mcp-config/huggingface-mcp-setup.md"
echo ""
echo "🎉 Hugging Face MCP 安装完成！" 