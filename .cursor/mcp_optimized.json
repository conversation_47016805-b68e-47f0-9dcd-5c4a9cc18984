{"mcpServers": {"sequential-thinking-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "toolFilter": {"include": ["mcp_memory_create_entities", "mcp_memory_search_nodes", "mcp_memory_read_graph", "mcp_memory_add_observations"]}}, "playwright-official": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-playwright"], "toolFilter": {"include": ["browser_snapshot", "browser_evaluate", "browser_screenshot", "browser_navigate"]}}, "huggingface-mcp": {"command": "npx", "args": ["-y", "huggingface-mcp-server"], "env": {"HUGGINGFACE_API_KEY": "hf_your_token_here"}}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-0a2c801f433d4718bcd8189f2742edf4"}}}}