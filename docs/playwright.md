# Playwright 完整学习教程

## 📚 目录
1. [什么是Playwright](#什么是playwright)
2. [为什么选择Playwright](#为什么选择playwright)
3. [安装和配置](#安装和配置)
4. [基础概念](#基础概念)
5. [选择器详解](#选择器详解)
6. [常用操作](#常用操作)
7. [实际案例](#实际案例)
8. [高级技巧](#高级技巧)
9. [异步编程](#异步编程)
10. [测试框架集成](#测试框架集成)
11. [错误处理](#错误处理)
12. [性能优化](#性能优化)
13. [常见问题](#常见问题)
14. [应用场景扩展](#应用场景扩展)

---

## 🤖 什么是Playwright

### 简单理解
Playwright就像一个**虚拟的机器人**，可以帮你：
- 自动打开浏览器
- 自动点击网页按钮
- 自动填写表单
- 自动截图
- 自动收集网页信息

### 类比理解
想象你有一个**数字助手**：
- 你告诉它："去百度搜索'天气'"
- 它就会自动打开浏览器，访问百度，输入"天气"，点击搜索
- 然后把搜索结果告诉你

### 实际应用场景
- **自动化测试**: 测试网站功能是否正常
- **数据采集**: 批量获取网页信息
- **监控系统**: 检查网站是否正常运行
- **自动化工具**: 重复性任务自动化

---

## 🏆 为什么选择Playwright

### 优势对比

| 特性 | Playwright | Selenium | Puppeteer |
|------|------------|----------|-----------|
| 支持浏览器 | Chrome, Firefox, Safari | 所有浏览器 | 仅Chrome |
| 速度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 稳定性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 学习难度 | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| 功能丰富度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

### 主要优势
1. **跨浏览器支持**: 一个代码运行在Chrome、Firefox、Safari
2. **自动等待**: 智能等待页面加载，不需要手动设置延时
3. **强大的选择器**: 支持多种元素定位方式
4. **移动端支持**: 可以模拟手机浏览器
5. **网络拦截**: 可以拦截和修改网络请求
6. **视频录制**: 可以录制操作过程
7. **PDF生成**: 可以将网页转换为PDF

---

## 🛠️ 安装和配置

### 1. 安装Python (如果还没有)
```bash
# 下载并安装Python 3.8+
# 访问 https://www.python.org/downloads/
```

### 2. 安装Playwright
```bash
# 安装Playwright包
pip install playwright

# 安装浏览器
playwright install

# 安装特定浏览器
playwright install chromium  # 只安装Chrome
playwright install firefox   # 只安装Firefox
playwright install webkit    # 只安装Safari
```

### 3. 验证安装
```python
# 创建test_install.py文件
from playwright.sync_api import sync_playwright

def test_installation():
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch()
        # 创建新页面
        page = browser.new_page()
        # 访问网站
        page.goto("https://www.baidu.com")
        # 截图
        page.screenshot(path="test.png")
        # 关闭浏览器
        browser.close()
        print("✅ Playwright安装成功！")

if __name__ == "__main__":
    test_installation()
```

### 4. 开发环境配置
```bash
# 安装开发工具
pip install pytest-playwright

# 生成测试配置
playwright install --with-deps
```

---

## 🧩 基础概念

### 1. 浏览器 (Browser)
```python
# 就像你电脑上的Chrome、Firefox一样
browser = playwright.chromium.launch()  # 启动Chrome
browser = playwright.firefox.launch()   # 启动Firefox
browser = playwright.webkit.launch()    # 启动Safari

# 浏览器启动选项
browser = p.chromium.launch(
    headless=False,  # 显示浏览器窗口
    slow_mo=1000,    # 慢动作模式
    args=['--no-sandbox']  # 启动参数
)
```

### 2. 页面 (Page)
```python
# 就像浏览器中的一个标签页
page = browser.new_page()  # 新建标签页
page.goto("https://www.baidu.com")  # 访问网站

# 页面设置
page.set_viewport_size({"width": 1920, "height": 1080})  # 设置窗口大小
page.set_extra_http_headers({"User-Agent": "Custom Agent"})  # 设置请求头
```

### 3. 元素 (Element)
```python
# 网页上的按钮、输入框、图片等
button = page.locator("button")  # 找到按钮
input_box = page.locator("input")  # 找到输入框

# 元素操作
element = page.locator(".my-class")
element.click()  # 点击
element.fill("text")  # 填写文字
element.text_content()  # 获取文本
```

### 4. 上下文 (Context)
```python
# 浏览器上下文，包含多个页面
context = browser.new_context()
page1 = context.new_page()
page2 = context.new_page()

# 上下文设置
context = browser.new_context(
    viewport={"width": 1920, "height": 1080},
    user_agent="Custom User Agent",
    locale="zh-CN"
)
```

---

## 🎯 选择器详解

### 1. 基础选择器
```python
# CSS选择器
page.click("button")  # 标签选择器
page.click("#login-btn")  # ID选择器
page.click(".submit-button")  # 类选择器
page.click("input[name='username']")  # 属性选择器

# 文本选择器
page.click("text=登录")  # 精确文本
page.click("text=登录 >> button")  # 文本+元素
page.click("text=/登录|注册/")  # 正则表达式
```

### 2. 高级选择器
```python
# 包含文本
page.click("button:has-text('提交')")
page.click("div:has(> img)")  # 包含子元素

# 位置选择器
page.click("button >> nth=0")  # 第一个按钮
page.click("button >> nth=-1")  # 最后一个按钮

# 组合选择器
page.click("form:has(input[name='email']) >> button")
page.click("div.container >> button.primary")
```

### 3. 选择器最佳实践
```python
# 好的选择器 - 稳定可靠
page.click("#login-button")  # 使用ID
page.click("button:has-text('登录')")  # 使用文本
page.click("[data-testid='submit']")  # 使用测试ID

# 避免的选择器 - 容易失效
page.click("div:nth-child(3)")  # 位置依赖
page.click(".btn-primary")  # 样式依赖
page.click("button")  # 太宽泛
```

### 4. 选择器调试
```python
# 检查元素是否存在
if page.locator("button").count() > 0:
    page.click("button")

# 等待元素出现
page.wait_for_selector("button", timeout=10000)

# 获取元素信息
element = page.locator("button")
print(f"元素数量: {element.count()}")
print(f"是否可见: {element.is_visible()}")
```

---

## 🎮 常用操作

### 1. 打开网页
```python
# 访问网站
page.goto("https://www.baidu.com")

# 等待页面加载完成
page.wait_for_load_state("networkidle")

# 设置超时时间
page.goto("https://example.com", timeout=30000)
```

### 2. 点击操作
```python
# 基础点击
page.click("button")

# 点击链接
page.click("a[href='/login']")

# 点击图片
page.click("img[alt='logo']")

# 点击特定文本
page.click("text=登录")

# 右键点击
page.click("button", button="right")

# 双击
page.dblclick("button")
```

### 3. 输入文字
```python
# 在输入框中输入文字
page.fill("input[name='username']", "用户名")
page.fill("input[name='password']", "密码")

# 清空输入框
page.fill("input", "")

# 模拟打字（逐个字符输入）
page.type("input", "慢慢打字", delay=100)

# 追加文字
page.press("input", "End")
page.type("input", "追加内容")
```

### 4. 选择下拉菜单
```python
# 选择下拉菜单选项
page.select_option("select[name='country']", "中国")
page.select_option("select", "选项值")

# 多选
page.select_option("select[name='skills']", ["Python", "JavaScript"])

# 通过标签选择
page.select_option("select", label="中国")
```

### 5. 截图
```python
# 截取整个页面
page.screenshot(path="full_page.png")

# 截取特定元素
page.locator(".header").screenshot(path="header.png")

# 截取可见区域
page.screenshot(path="visible.png", full_page=False)

# 设置截图质量
page.screenshot(path="high_quality.png", quality=90)
```

### 6. 等待操作
```python
# 等待元素出现
page.wait_for_selector("button")

# 等待文本出现
page.wait_for_selector("text=加载完成")

# 等待网络请求完成
page.wait_for_load_state("networkidle")

# 等待元素消失
page.wait_for_selector("loading", state="hidden")
```

### 7. 获取信息
```python
# 获取页面标题
title = page.title()
print(f"页面标题: {title}")

# 获取元素文本
text = page.locator("h1").text_content()
print(f"标题文本: {text}")

# 获取元素属性
href = page.locator("a").get_attribute("href")
print(f"链接地址: {href}")

# 获取多个元素
links = page.locator("a").all()
for link in links:
    print(link.text_content())

# 获取页面URL
current_url = page.url
print(f"当前页面: {current_url}")
```

---

## 🎯 实际案例

### 案例1: 自动搜索百度
```python
from playwright.sync_api import sync_playwright

def search_baidu():
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(headless=False)  # headless=False显示浏览器
        page = browser.new_page()
        
        # 访问百度
        page.goto("https://www.baidu.com")
        
        # 输入搜索词
        page.fill("#kw", "Playwright教程")
        
        # 点击搜索按钮
        page.click("#su")
        
        # 等待搜索结果
        page.wait_for_selector(".result")
        
        # 截图保存
        page.screenshot(path="baidu_search.png")
        
        # 获取搜索结果
        results = page.locator(".result").all()
        for i, result in enumerate(results[:5]):
            title = result.locator("h3").text_content()
            print(f"结果{i+1}: {title}")
        
        browser.close()

if __name__ == "__main__":
    search_baidu()
```

### 案例2: 自动登录网站
```python
def auto_login():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 访问登录页面
        page.goto("https://example.com/login")
        
        # 填写用户名和密码
        page.fill("input[name='username']", "your_username")
        page.fill("input[name='password']", "your_password")
        
        # 点击登录按钮
        page.click("button[type='submit']")
        
        # 等待登录成功
        page.wait_for_selector(".dashboard")
        
        print("✅ 登录成功！")
        
        browser.close()
```

### 案例3: 网页数据采集
```python
def scrape_news():
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 访问新闻网站
        page.goto("https://news.example.com")
        
        # 获取新闻标题
        headlines = page.locator(".headline").all()
        
        print("📰 今日新闻:")
        for i, headline in enumerate(headlines[:10]):
            title = headline.text_content()
            link = headline.locator("a").get_attribute("href")
            print(f"{i+1}. {title}")
            print(f"   链接: {link}")
            print()
        
        browser.close()
```

### 案例4: 表单自动填写
```python
def fill_form():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 访问表单页面
        page.goto("https://example.com/form")
        
        # 填写个人信息
        page.fill("input[name='name']", "张三")
        page.fill("input[name='email']", "<EMAIL>")
        page.fill("input[name='phone']", "13800138000")
        
        # 选择性别
        page.click("input[value='male']")
        
        # 选择城市
        page.select_option("select[name='city']", "北京")
        
        # 勾选同意条款
        page.click("input[type='checkbox']")
        
        # 提交表单
        page.click("button[type='submit']")
        
        # 等待提交成功
        page.wait_for_selector(".success-message")
        
        print("✅ 表单提交成功！")
        
        browser.close()
```

### 案例5: 电商价格监控
```python
def monitor_prices():
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 访问电商网站
        page.goto("https://example-shop.com/product/123")
        
        # 获取商品信息
        product_name = page.locator(".product-title").text_content()
        current_price = page.locator(".price").text_content()
        
        print(f"商品: {product_name}")
        print(f"价格: {current_price}")
        
        # 检查是否有优惠
        if page.locator(".discount").count() > 0:
            discount = page.locator(".discount").text_content()
            print(f"优惠: {discount}")
        
        browser.close()
```

### 案例6: 社交媒体自动化
```python
def auto_post():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 登录社交媒体
        page.goto("https://twitter.com/login")
        page.fill("input[name='username']", "your_username")
        page.fill("input[name='password']", "your_password")
        page.click("button[type='submit']")
        
        # 发布内容
        page.wait_for_selector("[data-testid='tweetTextarea']")
        page.fill("[data-testid='tweetTextarea']", "Hello Playwright!")
        page.click("[data-testid='tweetButton']")
        
        print("✅ 发布成功！")
        browser.close()
```

---

## 🚀 高级技巧

### 1. 处理弹窗和对话框
```python
# 处理确认对话框
page.on("dialog", lambda dialog: dialog.accept())

# 处理提示对话框
page.on("dialog", lambda dialog: dialog.dismiss())

# 处理文件上传
page.set_input_files("input[type='file']", "path/to/file.jpg")

# 处理多个文件上传
page.set_input_files("input[type='file']", ["file1.jpg", "file2.jpg"])
```

### 2. 模拟键盘操作
```python
# 按Enter键
page.press("input", "Enter")

# 按Tab键切换焦点
page.press("input", "Tab")

# 组合键
page.keyboard.press("Control+A")  # 全选
page.keyboard.press("Control+C")  # 复制
page.keyboard.press("Control+V")  # 粘贴

# 特殊按键
page.keyboard.press("ArrowDown")  # 向下箭头
page.keyboard.press("Escape")     # ESC键
```

### 3. 模拟鼠标操作
```python
# 鼠标悬停
page.hover("button")

# 拖拽操作
page.drag_and_drop("source", "target")

# 右键点击
page.click("button", button="right")

# 鼠标滚轮
page.mouse.wheel(0, 100)  # 向下滚动
page.mouse.wheel(0, -100) # 向上滚动
```

### 4. 网络请求拦截
```python
# 拦截网络请求
page.route("**/*.{png,jpg,jpeg}", lambda route: route.abort())

# 修改请求
def handle_route(route):
    if route.request.resource_type == "image":
        route.abort()
    else:
        route.continue_()

page.route("**/*", handle_route)

# 模拟网络请求
page.route("**/api/data", lambda route: route.fulfill(
    status=200,
    content_type="application/json",
    body='{"data": "mocked"}'
))
```

### 5. 多标签页操作
```python
# 创建新标签页
page2 = browser.new_page()
page2.goto("https://example.com")

# 在标签页间切换
page.bring_to_front()

# 获取所有标签页
pages = context.pages
for i, p in enumerate(pages):
    print(f"标签页{i}: {p.url}")
```

### 6. 移动端模拟
```python
# 模拟手机浏览器
page = browser.new_page(viewport={"width": 375, "height": 667})
page.goto("https://m.example.com")

# 模拟触摸操作
page.touchscreen.tap(100, 200)
```

### 7. 视频录制
```python
# 录制视频
context = browser.new_context(
    record_video_dir="./videos/",
    record_video_size={"width": 1280, "height": 720}
)
page = context.new_page()
# ... 执行操作 ...
context.close()
```

### 8. PDF生成
```python
# 生成PDF
page.pdf(path="page.pdf")

# 设置PDF选项
page.pdf(
    path="page.pdf",
    format="A4",
    print_background=True,
    margin={"top": "1cm", "bottom": "1cm"}
)
```

---

## ⚡ 异步编程

### 1. 异步基础
```python
import asyncio
from playwright.async_api import async_playwright

async def async_example():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        await page.goto("https://example.com")
        await browser.close()

# 运行异步函数
asyncio.run(async_example())
```

### 2. 并发操作
```python
async def concurrent_operations():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        
        # 创建多个页面并发操作
        pages = []
        for i in range(3):
            page = await browser.new_page()
            pages.append(page)
        
        # 并发访问多个网站
        tasks = [
            page.goto(f"https://site{i}.com") 
            for i, page in enumerate(pages)
        ]
        await asyncio.gather(*tasks)
        
        await browser.close()
```

### 3. 异步等待
```python
async def async_waiting():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        
        # 异步等待元素
        await page.wait_for_selector("button")
        
        # 异步等待网络请求
        await page.wait_for_load_state("networkidle")
        
        await browser.close()
```

---

## 🧪 测试框架集成

### 1. Pytest集成
```python
import pytest
from playwright.sync_api import Page

def test_search(page: Page):
    page.goto("https://www.baidu.com")
    page.fill("#kw", "Playwright")
    page.click("#su")
    assert page.locator(".result").count() > 0

def test_login(page: Page):
    page.goto("https://example.com/login")
    page.fill("input[name='username']", "test_user")
    page.fill("input[name='password']", "test_pass")
    page.click("button[type='submit']")
    assert page.locator(".dashboard").is_visible()
```

### 2. Pytest配置
```python
# pytest.ini
[tool:pytest]
addopts = --headed --slowmo 1000
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
```

### 3. 测试夹具
```python
import pytest
from playwright.sync_api import Page, Browser

@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    return {
        **browser_context_args,
        "viewport": {"width": 1920, "height": 1080},
        "ignore_https_errors": True
    }

@pytest.fixture
def authenticated_page(page: Page):
    page.goto("https://example.com/login")
    page.fill("input[name='username']", "test_user")
    page.fill("input[name='password']", "test_pass")
    page.click("button[type='submit']")
    return page
```

---

## 🛡️ 错误处理

### 1. 基础错误处理
```python
from playwright.sync_api import sync_playwright, TimeoutError

def safe_click(page, selector):
    try:
        page.click(selector)
        return True
    except TimeoutError:
        print(f"元素 {selector} 未找到")
        return False
    except Exception as e:
        print(f"点击失败: {e}")
        return False
```

### 2. 重试机制
```python
import time
from playwright.sync_api import TimeoutError

def retry_click(page, selector, max_attempts=3):
    for attempt in range(max_attempts):
        try:
            page.click(selector)
            return True
        except TimeoutError:
            if attempt < max_attempts - 1:
                print(f"重试第 {attempt + 1} 次...")
                time.sleep(1)
            else:
                print(f"元素 {selector} 最终未找到")
                return False
```

### 3. 条件等待
```python
def wait_for_condition(page, condition_func, timeout=10000):
    start_time = time.time()
    while time.time() - start_time < timeout / 1000:
        try:
            if condition_func(page):
                return True
        except:
            pass
        time.sleep(0.1)
    return False

# 使用示例
def is_page_loaded(page):
    return page.locator(".content").is_visible()

wait_for_condition(page, is_page_loaded)
```

### 4. 异常恢复
```python
def robust_navigation(page, url, fallback_url=None):
    try:
        page.goto(url)
    except Exception as e:
        print(f"访问 {url} 失败: {e}")
        if fallback_url:
            print(f"尝试访问备用地址: {fallback_url}")
            page.goto(fallback_url)
        else:
            raise e
```

---

## ⚡ 性能优化

### 1. 浏览器启动优化
```python
# 优化启动参数
browser = p.chromium.launch(
    headless=True,  # 无头模式更快
    args=[
        '--disable-gpu',
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
    ]
)
```

### 2. 网络优化
```python
# 拦截不必要的资源
def block_resources(route):
    if route.request.resource_type in ['image', 'stylesheet', 'font']:
        route.abort()
    else:
        route.continue_()

page.route("**/*", block_resources)
```

### 3. 内存管理
```python
# 及时关闭页面和浏览器
def efficient_scraping():
    with sync_playwright() as p:
        browser = p.chromium.launch()
        try:
            for url in urls:
                page = browser.new_page()
                page.goto(url)
                # 处理页面
                page.close()  # 及时关闭页面
        finally:
            browser.close()  # 确保浏览器关闭
```

### 4. 并发优化
```python
import asyncio
from playwright.async_api import async_playwright

async def concurrent_scraping(urls):
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        
        async def scrape_url(url):
            page = await browser.new_page()
            await page.goto(url)
            # 处理页面
            await page.close()
        
        # 并发处理多个URL
        tasks = [scrape_url(url) for url in urls]
        await asyncio.gather(*tasks)
        
        await browser.close()
```

---

## 🔧 常见问题

### Q1: 元素找不到怎么办？
```python
# 方法1: 等待元素出现
page.wait_for_selector("button", timeout=10000)

# 方法2: 使用更精确的选择器
page.click("button:has-text('登录')")

# 方法3: 检查元素是否存在
if page.locator("button").count() > 0:
    page.click("button")

# 方法4: 使用备用选择器
selectors = ["#login-btn", "button:has-text('登录')", ".login-button"]
for selector in selectors:
    if page.locator(selector).count() > 0:
        page.click(selector)
        break
```

### Q2: 页面加载太慢怎么办？
```python
# 设置超时时间
page.goto("https://example.com", timeout=30000)

# 等待特定状态
page.wait_for_load_state("domcontentloaded")

# 优化网络请求
page.route("**/*.{png,jpg,jpeg,css,woff}", lambda route: route.abort())
```

### Q3: 如何调试代码？
```python
# 方法1: 显示浏览器
browser = p.chromium.launch(headless=False)

# 方法2: 慢动作模式
browser = p.chromium.launch(slow_mo=1000)

# 方法3: 暂停执行
page.pause()

# 方法4: 开启调试模式
browser = p.chromium.launch(
    headless=False,
    devtools=True
)
```

### Q4: 如何处理验证码？
```python
# 方法1: 等待人工输入
page.wait_for_selector("input[name='captcha']")
input("请在浏览器中输入验证码，然后按回车...")

# 方法2: 使用OCR识别（需要额外库）
# pip install pytesseract pillow
import pytesseract
from PIL import Image

def solve_captcha(page):
    captcha_element = page.locator("img[alt='captcha']")
    captcha_element.screenshot(path="captcha.png")
    
    image = Image.open("captcha.png")
    text = pytesseract.image_to_string(image)
    
    page.fill("input[name='captcha']", text)
```

### Q5: 如何保存登录状态？
```python
import json

# 保存cookies
cookies = page.context.cookies()
with open("cookies.json", "w") as f:
    json.dump(cookies, f)

# 加载cookies
with open("cookies.json", "r") as f:
    cookies = json.load(f)
page.context.add_cookies(cookies)
```

### Q6: 如何处理动态内容？
```python
# 等待JavaScript执行完成
page.wait_for_function("() => document.querySelector('.dynamic-content')")

# 等待网络请求完成
page.wait_for_response("**/api/data")

# 轮询检查元素
def wait_for_dynamic_element(page, selector, timeout=10000):
    start_time = time.time()
    while time.time() - start_time < timeout / 1000:
        if page.locator(selector).count() > 0:
            return True
        time.sleep(0.1)
    return False
```

---

## 🌟 应用场景扩展

### 1. 电商自动化
```python
def ecommerce_automation():
    """电商自动化场景"""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 场景1: 商品价格监控
        page.goto("https://amazon.com/product/123")
        price = page.locator(".price").text_content()
        if float(price.replace("$", "")) < 50:
            print("价格下降！")
        
        # 场景2: 自动下单
        page.goto("https://shop.com/product/456")
        page.click("button:has-text('加入购物车')")
        page.click("button:has-text('结算')")
        page.fill("input[name='email']", "<EMAIL>")
        page.click("button:has-text('确认订单')")
        
        browser.close()
```

### 2. 社交媒体管理
```python
def social_media_automation():
    """社交媒体自动化"""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 场景1: 自动发布内容
        page.goto("https://twitter.com/compose/tweet")
        page.fill("[data-testid='tweetTextarea']", "Hello Playwright!")
        page.click("[data-testid='tweetButton']")
        
        # 场景2: 自动关注用户
        page.goto("https://twitter.com/user/suggestions")
        follow_buttons = page.locator("button:has-text('关注')").all()
        for button in follow_buttons[:5]:
            button.click()
        
        browser.close()
```

### 3. 内容采集系统
```python
def content_scraping_system():
    """内容采集系统"""
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 场景1: 新闻采集
        news_sites = [
            "https://news.example1.com",
            "https://news.example2.com",
            "https://news.example3.com"
        ]
        
        all_news = []
        for site in news_sites:
            page.goto(site)
            headlines = page.locator(".headline").all()
            for headline in headlines[:5]:
                title = headline.text_content()
                link = headline.locator("a").get_attribute("href")
                all_news.append({"title": title, "link": link, "source": site})
        
        # 保存到文件
        import json
        with open("news_data.json", "w", encoding="utf-8") as f:
            json.dump(all_news, f, ensure_ascii=False, indent=2)
        
        browser.close()
```

### 4. 网站监控系统
```python
def website_monitoring():
    """网站监控系统"""
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 监控网站列表
        sites_to_monitor = [
            "https://example1.com",
            "https://example2.com",
            "https://example3.com"
        ]
        
        for site in sites_to_monitor:
            try:
                # 检查网站可用性
                response = page.goto(site, timeout=10000)
                if response.status == 200:
                    print(f"✅ {site} 正常运行")
                    
                    # 检查关键功能
                    if page.locator(".login-button").count() > 0:
                        print(f"✅ {site} 登录功能正常")
                    
                    # 性能监控
                    load_time = page.evaluate("() => performance.timing.loadEventEnd - performance.timing.navigationStart")
                    print(f"⏱️ {site} 加载时间: {load_time}ms")
                    
                else:
                    print(f"❌ {site} 返回状态码: {response.status}")
                    
            except Exception as e:
                print(f"❌ {site} 访问失败: {e}")
        
        browser.close()
```

### 5. 自动化测试套件
```python
def comprehensive_testing():
    """综合自动化测试"""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 测试场景1: 用户注册流程
        page.goto("https://example.com/register")
        page.fill("input[name='username']", "testuser")
        page.fill("input[name='email']", "<EMAIL>")
        page.fill("input[name='password']", "password123")
        page.click("button[type='submit']")
        
        # 验证注册成功
        assert page.locator(".success-message").is_visible()
        
        # 测试场景2: 搜索功能
        page.goto("https://example.com/search")
        page.fill("input[name='q']", "test query")
        page.click("button[type='submit']")
        
        # 验证搜索结果
        assert page.locator(".search-result").count() > 0
        
        # 测试场景3: 购物车功能
        page.goto("https://example.com/product/123")
        page.click("button:has-text('加入购物车')")
        page.click("a:has-text('购物车')")
        
        # 验证购物车
        assert page.locator(".cart-item").count() > 0
        
        browser.close()
```

### 6. 数据验证系统
```python
def data_validation_system():
    """数据验证系统"""
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 验证数据一致性
        test_data = [
            {"name": "张三", "email": "<EMAIL>"},
            {"name": "李四", "email": "<EMAIL>"},
            {"name": "王五", "email": "<EMAIL>"}
        ]
        
        for data in test_data:
            page.goto("https://example.com/user-profile")
            page.fill("input[name='name']", data["name"])
            page.fill("input[name='email']", data["email"])
            page.click("button:has-text('验证')")
            
            # 检查验证结果
            if page.locator(".validation-success").is_visible():
                print(f"✅ {data['name']} 数据验证通过")
            else:
                print(f"❌ {data['name']} 数据验证失败")
        
        browser.close()
```

### 7. 自动化报告生成
```python
def automated_reporting():
    """自动化报告生成"""
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 收集数据
        page.goto("https://analytics.example.com")
        
        # 获取关键指标
        metrics = {}
        metrics["page_views"] = page.locator(".page-views").text_content()
        metrics["unique_visitors"] = page.locator(".unique-visitors").text_content()
        metrics["conversion_rate"] = page.locator(".conversion-rate").text_content()
        
        # 生成图表
        page.goto("https://chart.example.com")
        page.fill("input[name='data']", str(metrics))
        page.click("button:has-text('生成图表')")
        
        # 截图保存
        page.screenshot(path="report.png")
        
        # 生成PDF报告
        page.pdf(path="monthly_report.pdf")
        
        browser.close()
```

### 8. 多平台同步
```python
def multi_platform_sync():
    """多平台内容同步"""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        
        # 准备内容
        content = "这是一条测试内容 #Playwright #自动化"
        
        # 平台列表
        platforms = [
            {"name": "Twitter", "url": "https://twitter.com/compose/tweet"},
            {"name": "LinkedIn", "url": "https://linkedin.com/post"},
            {"name": "Facebook", "url": "https://facebook.com/status"}
        ]
        
        for platform in platforms:
            try:
                page = browser.new_page()
                page.goto(platform["url"])
                
                # 填写内容
                if platform["name"] == "Twitter":
                    page.fill("[data-testid='tweetTextarea']", content)
                    page.click("[data-testid='tweetButton']")
                elif platform["name"] == "LinkedIn":
                    page.fill("textarea[name='content']", content)
                    page.click("button:has-text('发布')")
                elif platform["name"] == "Facebook":
                    page.fill("textarea[name='status']", content)
                    page.click("button:has-text('发布')")
                
                print(f"✅ {platform['name']} 发布成功")
                page.close()
                
            except Exception as e:
                print(f"❌ {platform['name']} 发布失败: {e}")
        
        browser.close()
```

---

## 📚 学习资源

### 官方文档
- [Playwright官方文档](https://playwright.dev/)
- [Python API文档](https://playwright.dev/python/docs/intro)
- [选择器指南](https://playwright.dev/python/docs/selectors)
- [最佳实践](https://playwright.dev/python/docs/best-practices)

### 实践项目
1. **自动化测试项目**: 为你的网站写自动化测试
2. **数据采集项目**: 采集新闻、商品价格等数据
3. **监控项目**: 监控网站可用性和性能
4. **自动化工具**: 自动填写表单、提交数据
5. **社交媒体管理**: 自动发布内容、管理账号
6. **电商自动化**: 价格监控、自动下单
7. **内容管理系统**: 批量处理内容、生成报告

### 进阶学习
1. **测试框架集成**: 与pytest、unittest集成
2. **CI/CD集成**: 与GitHub Actions、Jenkins集成
3. **性能测试**: 进行网站性能测试
4. **移动端测试**: 测试手机应用
5. **API测试**: 结合API测试工具
6. **安全测试**: 进行安全漏洞检测

### 社区资源
- [Playwright GitHub](https://github.com/microsoft/playwright)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/playwright)
- [Discord社区](https://discord.gg/playwright)

---

## 🎉 总结

Playwright是一个强大的浏览器自动化工具，通过本教程你应该已经掌握了：

✅ **基础概念**: 浏览器、页面、元素、选择器  
✅ **选择器详解**: 多种定位方式和最佳实践  
✅ **常用操作**: 点击、输入、截图、等待  
✅ **实际应用**: 搜索、登录、数据采集、表单填写  
✅ **高级技巧**: 弹窗处理、键盘操作、网络拦截  
✅ **异步编程**: 异步操作和并发处理  
✅ **测试框架**: 与pytest等框架集成  
✅ **错误处理**: 异常处理和重试机制  
✅ **性能优化**: 启动优化和网络优化  
✅ **应用场景**: 8个实际应用场景示例  

现在你可以开始用Playwright来：
- 🧪 写自动化测试
- 🕷️ 做网页爬虫
- 📊 采集数据
- 🤖 自动化重复任务
- 📱 管理社交媒体
- 🛒 监控电商价格
- 📈 生成自动化报告
- 🔄 多平台内容同步

记住：**实践是最好的老师**，多写代码，多尝试不同的场景！

---

**祝你学习愉快！** 🚀 

