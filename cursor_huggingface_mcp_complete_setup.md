# Cursor Hugging Face MCP 完整配置指南

## 🎉 配置完成状态

✅ **Hugging Face MCP 包已安装**: `huggingface-mcp-server`  
✅ **Cursor MCP 配置文件已创建**: `.cursor/mcp.json`  
✅ **配置测试通过**: 所有组件正常工作  
⚠️ **需要完成**: 配置 Hugging Face API Token  

## 📋 当前配置

### 已安装的 MCP 工具
1. **sequential-thinking-mcp** (1个工具) - 思维链推理
2. **memory** (9个工具) - 知识图谱和记忆管理  
3. **huggingface-mcp** (新增) - Hugging Face 模型和数据集管理
4. **firecrawl-mcp** (已配置) - 网页爬取和数据提取

### 工具数量统计
- 当前工具总数: 54个 (超过建议的40个)
- 建议优化: 减少14个以上工具以提高性能

## 🔧 下一步操作

### 1. 获取 Hugging Face API Token
1. 访问: https://huggingface.co/settings/tokens
2. 登录您的账户
3. 点击 "New token"
4. 输入名称（如: "Cursor MCP"）
5. 选择权限（建议: Read）
6. 点击 "Generate token"
7. 复制生成的 Token（以 "hf_" 开头）

### 2. 更新配置文件
打开文件 `.cursor/mcp.json`，找到以下配置项：
```json
"huggingface-mcp": {
  "command": "npx",
  "args": ["-y", "huggingface-mcp-server"],
  "env": {
    "HUGGINGFACE_API_KEY": "hf_your_token_here"  // 替换这里
  }
}
```

将 `"hf_your_token_here"` 替换为您的真实 API Token。

### 3. 重启 Cursor
1. 完全关闭 Cursor
2. 重新启动 Cursor
3. 检查 MCP 工具是否可用

### 4. 验证配置
运行测试脚本验证配置：
```bash
python3 test_cursor_huggingface.py
```

## 🚀 功能特性

### 模型管理
- 🔍 **搜索模型**: 搜索和浏览 Hugging Face 模型
- 📥 **下载模型**: 下载模型到本地
- 📊 **查看详情**: 查看模型详情和元数据
- 🔄 **版本管理**: 管理模型版本

### 数据集管理
- 🔍 **搜索数据集**: 搜索和浏览数据集
- 📥 **下载数据集**: 下载数据集到本地
- 📊 **查看详情**: 查看数据集详情和结构
- 🔄 **版本管理**: 管理数据集版本

### 推理服务
- 🤖 **本地推理**: 使用本地模型进行推理
- ☁️ **云端推理**: 调用 Hugging Face 云端推理服务
- 📦 **批量处理**: 批量推理处理
- ⚙️ **参数配置**: 自定义推理参数

## 💡 使用示例

在 Cursor 中，您可以尝试以下操作：

### 搜索模型
```
搜索中文文本分类模型
搜索图像识别模型
搜索语音识别模型
```

### 下载模型
```
下载 bert-base-chinese 模型
下载 gpt2 模型
下载 resnet50 模型
```

### 数据集操作
```
搜索中文新闻数据集
搜索图像分类数据集
搜索语音数据集
```

### 推理服务
```
使用 bert-base-chinese 进行文本分类
使用 gpt2 生成文本
使用 resnet50 进行图像分类
```

## ⚠️ 重要提醒

### 1. 工具数量优化
- 当前有54个工具，建议控制在40个以内
- 可以在 Cursor 设置中禁用不需要的工具
- 优先保留核心功能工具

### 2. API 限制
- 注意 Hugging Face API 的调用限制
- 合理使用 API 配额
- 监控 API 使用情况

### 3. 存储空间
- 下载模型需要足够的本地存储空间
- 定期清理不需要的模型文件
- 监控磁盘使用情况

### 4. 网络连接
- 确保网络连接稳定
- 配置合适的超时时间
- 避免频繁的 API 调用

## 🔧 故障排除

### 常见问题

#### 1. Token 无效
**症状**: API 调用失败，返回 401 错误
**解决**: 
- 检查 Token 是否正确设置
- 确认 Token 权限是否足够
- 重新生成 Token

#### 2. 工具不显示
**症状**: 在 Cursor 中看不到 Hugging Face 工具
**解决**:
- 重启 Cursor
- 检查配置文件格式
- 验证 MCP 包安装

#### 3. 网络连接问题
**症状**: 无法连接到 Hugging Face API
**解决**:
- 检查网络连接
- 配置代理设置
- 检查防火墙设置

### 调试命令
```bash
# 测试 MCP 服务器
npx huggingface-mcp-server --help

# 测试 API 连接
curl -H "Authorization: Bearer YOUR_TOKEN" https://huggingface.co/api/models

# 检查配置文件
cat .cursor/mcp.json

# 运行测试脚本
python3 test_cursor_huggingface.py
```

## 📚 相关文档

- **配置指南**: `cursor_huggingface_guide.md`
- **工具管理**: `cursor_mcp_tools_management.md`
- **测试脚本**: `test_cursor_huggingface.py`
- **MCP 配置**: `.cursor/mcp.json`

## 🎯 最佳实践

### 1. 工具管理
- 定期评估工具使用情况
- 禁用不常用的工具
- 优先使用功能强大的工具

### 2. 安全考虑
- 妥善保管 API Token
- 定期轮换敏感信息
- 监控异常使用情况

### 3. 性能优化
- 控制工具数量在40个以内
- 定期清理缓存
- 监控内存使用情况

## 🎉 总结

恭喜！您已成功配置了 Cursor 的 Hugging Face MCP。现在您可以：

✅ **搜索和下载模型**  
✅ **管理数据集**  
✅ **进行模型推理**  
✅ **集成 Hugging Face 生态系统**  

请按照上述步骤完成 API Token 配置，然后重启 Cursor 开始使用！

---

**配置完成时间**: $(date +%Y-%m-%d %H:%M:%S)  
**配置状态**: 待完成 API Token 配置  
**下一步**: 获取并配置 Hugging Face API Token 