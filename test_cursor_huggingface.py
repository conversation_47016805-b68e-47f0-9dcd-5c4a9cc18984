#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Hugging Face MCP 测试脚本
"""

import os
import json
import subprocess
from pathlib import Path

def test_cursor_huggingface_mcp():
    """测试 Cursor Hugging Face MCP 配置"""
    
    print("🧪 测试 Cursor Hugging Face MCP 配置...")
    
    # 检查配置文件
    config_file = Path(".cursor/mcp.json")
    if not config_file.exists():
        print("❌ Cursor MCP 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" in config and "huggingface-mcp" in config["mcpServers"]:
            print("✅ Hugging Face MCP 配置已添加到 Cursor 配置文件中")
            
            # 检查 API Token
            huggingface_config = config["mcpServers"]["huggingface-mcp"]
            if "env" in huggingface_config and "HUGGINGFACE_API_KEY" in huggingface_config["env"]:
                api_key = huggingface_config["env"]["HUGGINGFACE_API_KEY"]
                if api_key != "hf_your_token_here":
                    print("✅ Hugging Face API Token 已配置")
                else:
                    print("⚠️  警告: 请将 Hugging Face API Token 替换为真实值")
            else:
                print("❌ 错误: Hugging Face API Token 未配置")
                return False
        else:
            print("❌ 错误: Hugging Face MCP 配置未找到")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件读取错误: {e}")
        return False
    
    # 检查 MCP 包安装
    try:
        result = subprocess.run(
            ["npx", "huggingface-mcp-server", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print("✅ Hugging Face MCP 包已正确安装")
        else:
            print("❌ Hugging Face MCP 包检查失败")
            return False
    except Exception as e:
        print(f"❌ MCP 包检查异常: {e}")
        return False
    
    print("\n🎉 Cursor Hugging Face MCP 配置测试完成！")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("Cursor Hugging Face MCP 配置测试")
    print("=" * 50)
    
    success = test_cursor_huggingface_mcp()
    
    if success:
        print("\n🎯 下一步操作:")
        print("1. 更新 API Token 配置")
        print("2. 重启 Cursor")
        print("3. 在 Cursor 中测试 Hugging Face MCP 功能")
    else:
        print("\n❌ 配置测试失败，请检查上述错误信息")
    
    print("=" * 50) 