#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hugging Face MCP 测试脚本
用于验证 Hugging Face MCP 配置是否正确
"""

import os
import json
import subprocess
import sys
from pathlib import Path

def test_huggingface_mcp():
    """测试 Hugging Face MCP 配置"""
    
    print("🧪 开始测试 Hugging Face MCP 配置...")
    
    # 1. 检查 Node.js 和 npm
    print("\n1. 检查 Node.js 环境...")
    try:
        node_version = subprocess.check_output(["node", "--version"], text=True).strip()
        npm_version = subprocess.check_output(["npm", "--version"], text=True).strip()
        print(f"✅ Node.js 版本: {node_version}")
        print(f"✅ npm 版本: {npm_version}")
    except subprocess.CalledProcessError as e:
        print(f"❌ Node.js 环境检查失败: {e}")
        return False
    
    # 2. 检查 Hugging Face MCP 包是否安装
    print("\n2. 检查 Hugging Face MCP 包...")
    try:
        result = subprocess.run(
            ["npx", "@modelcontextprotocol/server-huggingface", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print("✅ Hugging Face MCP 包已正确安装")
        else:
            print(f"❌ Hugging Face MCP 包检查失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Hugging Face MCP 包检查超时")
        return False
    except Exception as e:
        print(f"❌ Hugging Face MCP 包检查异常: {e}")
        return False
    
    # 3. 检查配置文件
    print("\n3. 检查配置文件...")
    config_file = Path("services/mcp-config/claude_desktop_config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if "mcpServers" in config and "huggingface-mcp" in config["mcpServers"]:
                print("✅ Hugging Face MCP 配置已添加到 Claude Desktop 配置文件中")
                
                # 检查 API Token 配置
                huggingface_config = config["mcpServers"]["huggingface-mcp"]
                if "env" in huggingface_config and "HUGGINGFACE_API_KEY" in huggingface_config["env"]:
                    api_key = huggingface_config["env"]["HUGGINGFACE_API_KEY"]
                    if api_key != "hf_your_token_here" and api_key != "YOUR_HUGGINGFACE_API_TOKEN_HERE":
                        print("✅ Hugging Face API Token 已配置")
                    else:
                        print("⚠️  警告: 请将 Hugging Face API Token 替换为真实值")
                else:
                    print("❌ 错误: Hugging Face API Token 未配置")
                    return False
            else:
                print("❌ 错误: Hugging Face MCP 配置未添加到 Claude Desktop 配置文件中")
                return False
        except json.JSONDecodeError as e:
            print(f"❌ 错误: 配置文件 JSON 格式错误: {e}")
            return False
    else:
        print("❌ 错误: Claude Desktop 配置文件不存在")
        return False
    
    # 4. 测试 API 连接（如果 Token 已配置）
    print("\n4. 测试 Hugging Face API 连接...")
    try:
        huggingface_config = config["mcpServers"]["huggingface-mcp"]
        api_key = huggingface_config["env"]["HUGGINGFACE_API_KEY"]
        
        if api_key not in ["hf_your_token_here", "YOUR_HUGGINGFACE_API_TOKEN_HERE"]:
            # 测试 API 连接
            import requests
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.get("https://huggingface.co/api/models", headers=headers, timeout=10)
            
            if response.status_code == 200:
                print("✅ Hugging Face API 连接成功")
            else:
                print(f"❌ Hugging Face API 连接失败: {response.status_code}")
                return False
        else:
            print("⚠️  跳过 API 连接测试（Token 未配置）")
    except Exception as e:
        print(f"❌ API 连接测试失败: {e}")
        return False
    
    print("\n🎉 Hugging Face MCP 配置测试完成！")
    print("\n📋 配置状态总结:")
    print("✅ Node.js 环境正常")
    print("✅ Hugging Face MCP 包已安装")
    print("✅ 配置文件已正确设置")
    print("✅ API 连接正常")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("Hugging Face MCP 配置测试工具")
    print("=" * 50)
    
    success = test_huggingface_mcp()
    
    if success:
        print("\n🎯 下一步操作:")
        print("1. 重启 Claude Desktop 以加载新配置")
        print("2. 在 Claude Desktop 中测试 Hugging Face MCP 功能")
        print("3. 尝试搜索和下载模型")
    else:
        print("\n❌ 配置测试失败，请检查上述错误信息")
        print("📚 参考配置指南: services/mcp-config/huggingface-mcp-setup.md")
    
    print("=" * 50)

if __name__ == "__main__":
    main() 