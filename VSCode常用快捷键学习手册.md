# VSCode 常用快捷键学习手册 📚

> **适用系统**: macOS  
> **更新时间**: 2025年1月  
> **难度等级**: 入门到进阶

---

## 🚀 基础编辑快捷键（必会）

### 文本选择
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+A` | 全选 | ⭐⭐⭐⭐⭐ |
| `Cmd+L` | 选择整行 | ⭐⭐⭐⭐ |
| `Cmd+D` | 选择当前单词（多次按选择相同单词） | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+L` | 选择所有相同单词 | ⭐⭐⭐⭐ |
| `Option+Shift+鼠标拖拽` | 多光标选择 | ⭐⭐⭐⭐ |

### 文本操作
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+C/V/X` | 复制/粘贴/剪切 | ⭐⭐⭐⭐⭐ |
| `Cmd+Z/Shift+Cmd+Z` | 撤销/重做 | ⭐⭐⭐⭐⭐ |
| `Cmd+/` | 注释/取消注释 | ⭐⭐⭐⭐⭐ |
| `Option+Shift+A` | 块注释 | ⭐⭐⭐ |
| `Cmd+]` / `Cmd+[` | 增加/减少缩进 | ⭐⭐⭐⭐ |

### 行操作
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Option+↑/↓` | 移动当前行 | ⭐⭐⭐⭐⭐ |
| `Option+Shift+↑/↓` | 复制当前行 | ⭐⭐⭐⭐ |
| `Cmd+Shift+K` | 删除当前行 | ⭐⭐⭐⭐ |
| `Cmd+Enter` | 在下方插入新行 | ⭐⭐⭐⭐ |
| `Cmd+Shift+Enter` | 在上方插入新行 | ⭐⭐⭐ |

---

## 📁 文件和窗口操作

### 文件管理
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+N` | 新建文件 | ⭐⭐⭐⭐⭐ |
| `Cmd+O` | 打开文件 | ⭐⭐⭐⭐ |
| `Cmd+S` | 保存文件 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+S` | 另存为 | ⭐⭐⭐ |
| `Cmd+W` | 关闭当前标签 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+T` | 重新打开关闭的标签 | ⭐⭐⭐⭐ |

### 标签页切换
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+Tab` | 切换应用程序 | ⭐⭐⭐⭐⭐ |
| `Ctrl+Tab` | 切换VSCode标签页 | ⭐⭐⭐⭐⭐ |
| `Cmd+1/2/3...` | 切换到第N个标签页 | ⭐⭐⭐⭐ |
| `Cmd+Option+←/→` | 切换标签页 | ⭐⭐⭐⭐ |

### 窗口分割
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+\` | 垂直分割编辑器 | ⭐⭐⭐⭐ |
| `Cmd+K Cmd+\` | 水平分割编辑器 | ⭐⭐⭐ |
| `Cmd+1/2/3` | 切换到第N个编辑器组 | ⭐⭐⭐⭐ |
| `Cmd+K Cmd+←/→` | 移动标签到其他编辑器组 | ⭐⭐⭐ |

---

## 🔍 搜索和导航

### 搜索功能
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+F` | 在当前文件中搜索 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+F` | 在整个项目中搜索 | ⭐⭐⭐⭐⭐ |
| `Cmd+H` | 替换 | ⭐⭐⭐⭐ |
| `Cmd+Shift+H` | 在整个项目中替换 | ⭐⭐⭐⭐ |
| `Cmd+G` / `Cmd+Shift+G` | 查找下一个/上一个 | ⭐⭐⭐⭐ |

### 快速导航
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+P` | 快速打开文件 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+P` | 命令面板 | ⭐⭐⭐⭐⭐ |
| `Cmd+G` | 跳转到指定行 | ⭐⭐⭐⭐ |
| `Cmd+Shift+O` | 跳转到符号（函数、变量等） | ⭐⭐⭐⭐ |
| `Cmd+T` | 在工作区中搜索符号 | ⭐⭐⭐ |

### 代码导航
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `F12` | 跳转到定义 | ⭐⭐⭐⭐⭐ |
| `Cmd+F12` | 跳转到实现 | ⭐⭐⭐⭐ |
| `Shift+F12` | 查找所有引用 | ⭐⭐⭐⭐ |
| `Cmd+←/→` | 跳转到行首/行尾 | ⭐⭐⭐⭐⭐ |
| `Cmd+↑/↓` | 跳转到文件开头/结尾 | ⭐⭐⭐⭐ |

---

## 🐛 调试和终端

### 调试快捷键
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `F5` | 开始调试 | ⭐⭐⭐⭐ |
| `F9` | 切换断点 | ⭐⭐⭐⭐ |
| `F10` | 单步跳过 | ⭐⭐⭐⭐ |
| `F11` | 单步进入 | ⭐⭐⭐⭐ |
| `Shift+F11` | 单步跳出 | ⭐⭐⭐ |

### 终端操作
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Ctrl+`` ` | 打开/关闭终端 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+`` ` | 新建终端 | ⭐⭐⭐⭐ |
| `Cmd+\` | 分割终端 | ⭐⭐⭐ |
| `Cmd+K` | 清空终端 | ⭐⭐⭐⭐ |

---

## 🎨 界面和视图

### 侧边栏控制
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+B` | 切换侧边栏显示 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+E` | 显示文件资源管理器 | ⭐⭐⭐⭐⭐ |
| `Cmd+Shift+F` | 显示搜索面板 | ⭐⭐⭐⭐ |
| `Cmd+Shift+G` | 显示Git面板 | ⭐⭐⭐⭐ |
| `Cmd+Shift+D` | 显示调试面板 | ⭐⭐⭐ |
| `Cmd+Shift+X` | 显示扩展面板 | ⭐⭐⭐ |

### 显示控制
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Cmd+J` | 切换面板显示 | ⭐⭐⭐⭐ |
| `Cmd+Shift+Y` | 显示调试控制台 | ⭐⭐⭐ |
| `Cmd+Shift+U` | 显示输出面板 | ⭐⭐⭐ |
| `Cmd+Shift+M` | 显示问题面板 | ⭐⭐⭐⭐ |

---

## 🔧 代码编辑进阶

### 多光标编辑
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Option+Click` | 添加光标 | ⭐⭐⭐⭐⭐ |
| `Cmd+Option+↑/↓` | 在上方/下方添加光标 | ⭐⭐⭐⭐ |
| `Cmd+U` | 撤销上一个光标操作 | ⭐⭐⭐ |
| `Escape` | 取消多光标 | ⭐⭐⭐⭐ |

### 代码格式化
| 快捷键 | 功能 | 使用频率 |
|--------|------|----------|
| `Option+Shift+F` | 格式化整个文档 | ⭐⭐⭐⭐⭐ |
| `Cmd+K Cmd+F` | 格式化选中代码 | ⭐⭐⭐⭐ |
| `Ctrl+Space` | 触发建议 | ⭐⭐⭐⭐⭐ |
| `Cmd+.` | 快速修复 | ⭐⭐⭐⭐⭐ |

---

## 📋 实用技巧组合

### 🔥 超实用组合技
1. **快速重构变量名**: `F2` → 输入新名称 → `Enter`
2. **快速复制行并修改**: `Option+Shift+↓` → 修改内容
3. **快速选择相同内容**: `Cmd+D` → `Cmd+D` → `Cmd+D`...
4. **快速打开文件**: `Cmd+P` → 输入文件名 → `Enter`
5. **快速执行命令**: `Cmd+Shift+P` → 输入命令 → `Enter`

### 💡 效率提升技巧
- **Emmet缩写**: 输入 `div.container` → `Tab` 生成HTML
- **代码片段**: 输入 `log` → `Tab` 生成console.log
- **智能感知**: `Ctrl+Space` 随时触发代码提示
- **面包屑导航**: `Cmd+Shift+.` 显示当前位置

---

## 🎯 学习建议

### 📈 学习优先级
1. **第一周**: 掌握基础编辑（复制粘贴、撤销重做、注释）
2. **第二周**: 学会文件操作和搜索功能
3. **第三周**: 掌握代码导航和多光标编辑
4. **第四周**: 学习调试和终端操作

### 🏃‍♂️ 练习方法
- **每天练习**: 选择5个快捷键重复使用
- **刻意练习**: 强迫自己用快捷键而不是鼠标
- **记忆卡片**: 制作快捷键记忆卡片
- **实际项目**: 在真实项目中应用学到的快捷键

### 📝 自定义快捷键
- 打开设置: `Cmd+,` → 搜索 "keyboard shortcuts"
- 或直接: `Cmd+K Cmd+S` 打开快捷键设置
- 可以根据个人习惯自定义快捷键

---

## 🎉 总结

掌握这些快捷键后，你的编码效率将提升**300%**以上！

**记住**: 
- 🎯 **不要贪多** - 每周学5-10个就够了
- 🔄 **重复练习** - 肌肉记忆需要时间
- 💪 **坚持使用** - 强迫自己用快捷键
- 🚀 **享受过程** - 感受效率提升的快感

---

*Happy Coding! 🚀*
