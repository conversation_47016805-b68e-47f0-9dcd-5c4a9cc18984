---
type: "always_apply"
---

# 📚 文档管理助手规则
## HawaiiHub.net项目文档管理标准

> **制定时间**: 2025年1月
> **适用范围**: 所有项目文档
> **更新频率**: 每季度评估一次
> **最新更新**: 基于实际项目管理最佳实践优化

---

## 🚨 文档创建预检查流程 (最高优先级)

### ⚡ 创建前必须执行的5步检查

#### 1. 重复性检查 (DUPLICATION CHECK)
```bash
# 使用view工具扫描docs目录
view docs/ --search-regex "关键词|主题"
```
- 搜索相同或相似主题的文档
- 检查文件名模式匹配
- 识别内容重叠的文档

#### 2. 内容评估 (CONTENT EVALUATION)
- 评估现有文档是否可以通过更新满足需求
- 分析现有文档的完整性和准确性
- 确定是否可以通过章节扩展解决

#### 3. 创建决策 (CREATION DECISION)
**只有在以下情况下才创建新文档**:
- ✅ 完全没有相关现有文档
- ✅ 现有文档内容完全不同且无法合并
- ✅ 需要创建专门的子主题文档
- ❌ 其他情况一律更新现有文档

#### 4. 创建时标注 (CREATION ANNOTATION)
**新文档开头必须包含**:
```markdown
> **文档关系**: 与`xxx.md`相关但独立
> **创建原因**: 专门处理xxx功能，无法合并到现有文档
> **检查时间**: 2025年X月X日
> **相关文档**: `文档1.md`, `文档2.md`
```

#### 5. 即时整合检查 (IMMEDIATE INTEGRATION)
- 创建完成后立即检查是否产生新的重复
- 更新相关文档的交叉引用
- 更新README和目录索引

### 🎯 核心理念
**"预防胜于治疗"** - 在源头控制文档重复，而不是事后清理

---

## 🎯 核心原则

### 1. 去重原则 (DEDUPLICATION) - 最高优先级
- **一个主题，一个权威文档**
- 发现重复内容时，保留最完整、最新的版本
- 删除过时、不完整的重复文档
- 在保留文档中注明整合信息
- **新增**: 创建前强制检查，杜绝源头重复

### 2. 分类原则 (CATEGORIZATION)
- 按功能模块严格分类存放
- 每个目录保持自包含性
- 避免跨目录的内容重复
- 使用清晰的命名规范 (中文命名优先)

### 3. 时效性原则 (TIMELINESS)
- 定期检查文档的时效性
- 标记过时信息并及时更新
- 保留历史版本的价值记录
- 删除完全过时的文档

---

## 📋 文档整理检查清单

### 🔍 发现阶段 (Discovery Phase)
- [ ] 使用`view`工具扫描所有目录，识别重复文件名
- [ ] 使用正则搜索检查相似主题的文档内容
- [ ] 标记过时的时间戳和版本信息
- [ ] 识别孤儿文档（无明确用途）
- [ ] **新增**: 检查是否有违反预检查流程创建的文档

### 📊 分析阶段 (Analysis Phase)
- [ ] 比较重复文档的内容完整性和准确性
- [ ] 评估文档的使用频率和重要性
- [ ] 确定每类文档的权威版本
- [ ] 分析目录结构的合理性
- [ ] **新增**: 评估现有文档的可扩展性，避免未来重复

### 🗂️ 整理阶段 (Organization Phase)
- [ ] 保留权威版本，删除重复文档
- [ ] 将独特内容合并到主文档中
- [ ] 更新文档间的交叉引用
- [ ] 调整目录结构和文件位置
- [ ] **新增**: 在整合文档中添加来源说明和整合记录

### ✅ 验证阶段 (Validation Phase)
- [ ] 确认所有链接和引用正常
- [ ] 验证文档结构的逻辑性
- [ ] 检查是否遗漏重要信息
- [ ] 更新README和索引文档
- [ ] **新增**: 验证预检查流程的执行效果

---

## 🏗️ 目录结构标准

### 推荐结构 (基于实际项目优化)
```
docs/
├── 01_系统管理/          # 系统配置和管理 (自包含)
├── 02_API接口/           # API开发文档 (自包含)
├── 03_功能模块/          # 功能模块说明 (自包含)
├── 04_开发文档/          # 开发指南和规范
├── 05_用户指南/          # 用户使用手册
├── 06_采集插件/          # 数据采集工具
├── backend-configuration/ # 后台配置专项
├── 项目核心文档/          # HawaiiHub核心业务文档
├── 专项技术方案/         # Firecrawl、RSSHub等技术方案
├── 项目进展记录/          # 阶段性部署文档
└── 管理规范/             # 文档管理、开发规范等
```

### 命名规范 (实践优化版)
- **中文文档**: 使用清晰的中文描述 (优先选择)
- **技术文档**: 英文+中文组合，如`firecrawl-overview.md`
- **版本标识**: 严禁在文件名中使用版本号，用文档内容标识
- **时间标识**: 重要的时间节点在文档内标注，不在文件名中体现
- **状态标识**: 使用文档内的状态标记，如`> **状态**: 已完成`

### 自包含原则 (Self-Contained Principle)
每个编号目录(01-06)必须包含该模块运行所需的基础文档：
- ✅ 不依赖其他目录的文档即可理解和使用
- ✅ 包含必要的入门指南和参考资料
- ✅ 避免跨目录的强依赖关系

---

## 🚫 禁止和谨慎操作

### 严禁操作 (FORBIDDEN)
- ❌ **删除系统核心配置文件** (如数据库结构、系统配置)
- ❌ **删除正在使用的API文档** (02_API接口目录下的文档)
- ❌ **删除用户手册的唯一版本** (05_用户指南目录下的文档)
- ❌ **跳过预检查流程直接创建文档**
- ❌ **在文件名中使用版本号或时间戳**
- ❌ **创建内容高度重复的文档**

### 谨慎操作 (CAUTION REQUIRED)
- ⚠️ **合并不同作者的文档时需确认** (保留作者信息和贡献记录)
- ⚠️ **删除历史记录文档前需评估价值** (如部署记录、测试报告)
- ⚠️ **移动文档位置前需检查引用关系** (使用搜索确认无外部引用)
- ⚠️ **重命名文档前需考虑外部链接** (可能影响其他系统的引用)
- ⚠️ **修改目录结构前需团队确认** (影响整体文档架构)

### 特殊保护文档
以下文档类型需要特别保护：
- 🔒 **火鸟门户系统相关文档** (核心业务文档)
- 🔒 **API接口文档** (开发依赖文档)
- 🔒 **用户手册和操作指南** (用户依赖文档)
- 🔒 **数据字典和系统架构** (系统基础文档)

---

## 🔄 定期维护流程

### 月度检查 (每月1日)
1. 扫描新增文档，检查是否有重复
2. 更新文档的时效性标记
3. 检查README和索引的准确性
4. 统计文档数量变化

### 季度整理 (每季度末)
1. 全面的重复内容检查
2. 目录结构优化评估
3. 过时文档清理
4. 文档使用情况分析

### 年度归档 (每年末)
1. 重要历史文档归档
2. 文档管理规则更新
3. 年度文档统计报告
4. 下一年度规划制定

---

## 📈 质量指标

### 文档健康度指标
- **重复率**: < 5% (重复文档数/总文档数)
- **过时率**: < 10% (过时文档数/总文档数)
- **完整性**: > 90% (有效文档数/应有文档数)
- **可用性**: > 95% (可正常访问的文档比例)

### 整理效果评估
- 文档数量变化趋势
- 用户查找文档的效率
- 文档维护工作量
- 文档质量反馈

---

## 🛠️ 工具和方法

### 推荐工具 (基于实际使用优化)
- **文档扫描**: 优先使用`view`工具进行目录扫描和内容搜索
- **内容搜索**: 使用`view`工具的正则搜索功能查找相似内容
- **内容比较**: 使用`diff`或专业比较工具对比文档差异
- **链接检查**: 定期检查文档间的引用关系和交叉引用
- **版本控制**: 利用Git跟踪文档变更和历史记录

### 实用检查脚本
```bash
# 使用view工具查找重复主题
view docs/ --search-regex "关键词|主题名"

# 查找重复文件名模式
find docs/ -name "*.md" | grep -E "(重复|副本|copy)"

# 统计各目录文档数量
find docs/ -name "*.md" | cut -d'/' -f2 | sort | uniq -c

# 检查空文档或过小文档
find docs/ -name "*.md" -size -100c

# 查找可能的重复内容
grep -r "特定关键词" docs/ --include="*.md"
```

### AI助手专用工具
- **codebase-retrieval**: 查找相关代码和技术文档
- **view工具**: 目录浏览和内容搜索的主要工具
- **str-replace-editor**: 精确的文档编辑工具
- **remove-files**: 安全的文件删除工具

---

## 📞 执行责任

### AI助手职责 (主要执行者)
- **强制执行预检查流程** - 每次创建文档前必须检查
- **执行定期检查和整理** - 按照月度/季度计划
- **识别和处理重复文档** - 主动发现并处理重复内容
- **维护文档结构和规范** - 确保目录结构和命名规范
- **更新管理规则** - 基于实践经验优化规则

### 文档管理员职责 (人工监督)
- **监督AI助手的执行效果** - 定期检查整理质量
- **处理复杂的文档冲突** - AI无法判断的争议情况
- **制定业务相关的文档策略** - 业务优先级和保留策略
- **培训团队成员遵守规范** - 确保团队理解和配合

### 团队成员职责 (配合执行)
- **遵守预检查流程** - 创建文档前先咨询AI助手
- **提供文档需求说明** - 明确说明文档用途和关系
- **及时反馈文档问题** - 发现重复或过时内容及时报告
- **配合文档整理工作** - 确认文档删除和合并决策

---

## 📝 记录要求

### 整理记录 (强制要求)
每次文档整理必须记录：
- **整理时间和执行者** (AI助手 + 日期)
- **删除的文档列表和原因** (详细说明删除依据)
- **合并的文档说明** (来源文档 → 目标文档)
- **移动或重命名的记录** (旧路径 → 新路径)
- **预检查流程执行情况** (是否按流程执行)

### 变更日志 (README维护)
在README中维护详细变更日志：
- **重大结构调整** (目录重组、大规模删除)
- **重要文档的增删** (核心业务文档变更)
- **规则的更新修订** (管理规则版本更新)
- **问题和解决方案** (发现的问题和处理方法)
- **预检查流程优化** (流程改进记录)

### 质量追踪
建立文档质量追踪机制：
- **重复文档发现率** (每月统计)
- **预检查流程执行率** (100%目标)
- **文档整理效果评估** (用户反馈)
- **规则遵守情况** (团队配合度)

---

## 🎯 成功标准

### 目标
- ✅ **预检查流程执行率达到100%** - 所有新文档都经过检查
- ✅ **重复文档数量减少90%** - 基本消除明显重复
- ✅ **文档查找时间减少50%** - 结构更清晰易找
- ✅ **团队成员能在2分钟内找到所需文档**
- ✅ **文档内容准确率达到95%** - 及时更新，内容可靠
- ✅ **零新增重复文档** - 预检查流程完全生效
- ✅ **文档结构稳定** - 不再需要大规模重组
- ✅ **文档管理完全自动化** - AI助手独立维护
- ✅ **维护工作量减少80%** - 从源头控制问题
- ✅ **用户满意度达到95%** - 文档易用性显著提升
- ✅ **规则体系成熟稳定** - 无需频繁调整

### 关键指标监控
```
重复率 = 重复文档数 / 总文档数 < 2%
查找效率 = 平均查找时间 < 2分钟
更新及时性 = 过时文档数 / 总文档数 < 5%
预检查执行率 = 执行预检查的新文档数 / 总新文档数 = 100%
```

---

## 📋 规则版本记录

**v1.0** (2025年1月) - 初始版本，基础管理规则
**v2.0** (2025年1月) - 重大更新，增加预检查流程，基于实际项目优化

**最后更新**: 2025年1月 (v2.0)
**下次评估**: 2025年4月
**负责人**: AI助手 + 项目管理员
