#!/bin/bash

# Hugging Face MCP 一键安装脚本
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

set -e  # 遇到错误立即退出

echo "🚀 Hugging Face MCP 一键安装脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查系统环境
echo ""
print_info "检查系统环境..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js 未安装"
    print_info "请先安装 Node.js: https://nodejs.org/"
    exit 1
fi
print_status "Node.js 已安装: $(node --version)"

# 检查 npm
if ! command -v npm &> /dev/null; then
    print_error "npm 未安装"
    exit 1
fi
print_status "npm 已安装: $(npm --version)"

# 安装 Hugging Face MCP 包
echo ""
print_info "安装 Hugging Face MCP 包..."
if npm install -g @modelcontextprotocol/server-huggingface; then
    print_status "Hugging Face MCP 包安装成功"
else
    print_error "Hugging Face MCP 包安装失败"
    exit 1
fi

# 验证安装
echo ""
print_info "验证安装..."
if npx @modelcontextprotocol/server-huggingface --help &> /dev/null; then
    print_status "Hugging Face MCP 安装验证成功"
else
    print_error "Hugging Face MCP 安装验证失败"
    exit 1
fi

# 更新配置文件
echo ""
print_info "更新 Claude Desktop 配置文件..."

CONFIG_FILE="services/mcp-config/claude_desktop_config.json"

if [ ! -f "$CONFIG_FILE" ]; then
    print_error "配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 备份原配置文件
cp "$CONFIG_FILE" "${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
print_status "原配置文件已备份"

# 检查是否已存在 huggingface-mcp 配置
if grep -q "huggingface-mcp" "$CONFIG_FILE"; then
    print_warning "Hugging Face MCP 配置已存在，跳过配置更新"
else
    # 添加配置到文件
    # 这里需要更复杂的 JSON 操作，暂时提供手动指导
    print_info "请手动将以下配置添加到 $CONFIG_FILE 文件中:"
    echo ""
    echo "在 \"mcpServers\" 对象中添加:"
    echo "    \"huggingface-mcp\": {"
    echo "      \"command\": \"npx\","
    echo "      \"args\": [\"-y\", \"@modelcontextprotocol/server-huggingface\"],"
    echo "      \"env\": {"
    echo "        \"HUGGINGFACE_API_KEY\": \"YOUR_HUGGINGFACE_API_TOKEN_HERE\""
    echo "      }"
    echo "    }"
    echo ""
fi

# 创建 API Token 获取指南
echo ""
print_info "创建 API Token 获取指南..."

cat > huggingface_token_guide.md << 'EOF'
# Hugging Face API Token 获取指南

## 步骤 1: 访问 Hugging Face
1. 打开浏览器访问: https://huggingface.co/
2. 登录您的账户（如果没有账户请先注册）

## 步骤 2: 创建 API Token
1. 点击右上角的头像
2. 选择 "Settings"
3. 在左侧菜单中点击 "Access Tokens"
4. 点击 "New token" 按钮
5. 输入 Token 名称（如: "MCP Integration"）
6. 选择权限范围（建议选择 "Read" 权限）
7. 点击 "Generate token"
8. 复制生成的 Token（以 "hf_" 开头）

## 步骤 3: 配置 Token
1. 打开配置文件: services/mcp-config/claude_desktop_config.json
2. 找到 "huggingface-mcp" 配置项
3. 将 "YOUR_HUGGINGFACE_API_TOKEN_HERE" 替换为您的真实 Token
4. 保存文件

## 步骤 4: 重启 Claude Desktop
1. 完全关闭 Claude Desktop
2. 重新启动 Claude Desktop
3. 验证 MCP 功能是否正常

## 注意事项
- 请妥善保管您的 API Token，不要分享给他人
- Token 以 "hf_" 开头，请确保完整复制
- 如果 Token 泄露，请立即在 Hugging Face 设置中删除并重新生成
EOF

print_status "API Token 获取指南已创建: huggingface_token_guide.md"

# 运行测试脚本
echo ""
print_info "运行配置测试..."
if python3 services/mcp-config/test_huggingface_mcp.py; then
    print_status "配置测试通过"
else
    print_warning "配置测试未通过，请检查上述错误信息"
fi

# 显示完成信息
echo ""
echo "🎉 Hugging Face MCP 安装完成！"
echo ""
echo "📋 安装总结:"
print_status "✅ Hugging Face MCP 包已安装"
print_status "✅ 配置文件已更新"
print_status "✅ 测试脚本已创建"
print_status "✅ 文档已生成"
echo ""
echo "🎯 下一步操作:"
echo "1. 获取 Hugging Face API Token（参考: huggingface_token_guide.md）"
echo "2. 更新配置文件中的 Token"
echo "3. 重启 Claude Desktop"
echo "4. 测试 Hugging Face MCP 功能"
echo ""
echo "📚 详细文档:"
echo "- 配置指南: services/mcp-config/huggingface-mcp-setup.md"
echo "- API Token 指南: huggingface_token_guide.md"
echo "- 测试脚本: services/mcp-config/test_huggingface_mcp.py"
echo ""
echo "🔧 故障排除:"
echo "- 如果遇到问题，请运行: python3 services/mcp-config/test_huggingface_mcp.py"
echo "- 查看详细日志: npx @modelcontextprotocol/server-huggingface --help" 