# MCP 单独工具管理指南

## 📊 当前工具分析

根据您的截图，当前启用的MCP工具包括：

### 1. sequential-thinking-mcp (1个工具)
- **功能**: 思维链推理，帮助AI进行复杂问题分析
- **工具数量**: 1个
- **建议**: 保留（核心推理功能）

### 2. memory (9个工具)
- **功能**: 知识图谱和记忆管理
- **工具数量**: 9个
- **建议**: 根据需要选择性启用

### 3. playwright-official (24个工具)
- **功能**: 浏览器自动化和网页操作
- **工具数量**: 24个（最大的工具集）
- **建议**: 根据项目需求选择性启用

### 4. 已禁用的工具
- filesystem-mcp: 文件系统操作
- time-mcp: 时间相关功能
- github: GitHub集成功能

## 🔧 单独关闭工具的方法

### 方法1: 通过Cursor界面
1. 在Cursor中打开MCP设置
2. 找到对应的MCP服务器
3. 点击工具旁边的编辑图标（如playwright-official的铅笔图标）
4. 在工具列表中取消勾选不需要的工具

### 方法2: 通过配置文件
编辑 `.cursor/mcp.json` 文件，为特定工具添加禁用配置：

```json
{
  "mcpServers": {
    "playwright-official": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-playwright"],
      "env": {
        "PLAYWRIGHT_BROWSERS_PATH": "0"
      },
      "disabledTools": [
        "browser_navigate",
        "browser_click",
        "browser_type",
        "browser_screenshot"
      ]
    }
  }
}
```

### 方法3: 使用工具过滤器
在配置文件中添加工具过滤器：

```json
{
  "mcpServers": {
    "playwright-official": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-playwright"],
      "toolFilter": {
        "include": ["browser_snapshot", "browser_evaluate"],
        "exclude": ["browser_navigate", "browser_click", "browser_type"]
      }
    }
  }
}
```

## 📋 工具功能详细分析

### Playwright-official (24个工具)
这是最大的工具集，包含以下功能：

#### 浏览器控制工具
- `browser_navigate`: 网页导航
- `browser_click`: 点击操作
- `browser_type`: 文本输入
- `browser_screenshot`: 截图
- `browser_snapshot`: 页面快照
- `browser_evaluate`: JavaScript执行

#### 文件操作工具
- `browser_file_upload`: 文件上传
- `browser_tab_list`: 标签页管理
- `browser_tab_new`: 新建标签页
- `browser_tab_select`: 选择标签页
- `browser_tab_close`: 关闭标签页

#### 高级功能工具
- `browser_drag`: 拖拽操作
- `browser_hover`: 悬停操作
- `browser_select_option`: 下拉选择
- `browser_wait_for`: 等待操作
- `browser_handle_dialog`: 对话框处理

### Memory (9个工具)
知识图谱和记忆管理工具：

#### 实体管理
- `mcp_memory_create_entities`: 创建实体
- `mcp_memory_add_observations`: 添加观察
- `mcp_memory_delete_entities`: 删除实体
- `mcp_memory_delete_observations`: 删除观察

#### 关系管理
- `mcp_memory_create_relations`: 创建关系
- `mcp_memory_delete_relations`: 删除关系

#### 查询功能
- `mcp_memory_read_graph`: 读取图谱
- `mcp_memory_search_nodes`: 搜索节点
- `mcp_memory_open_nodes`: 打开节点

## 🎯 优化建议

### 1. 按需启用策略
```
当前工具总数: 34个 (1+9+24)
目标工具数: 20-25个
可减少: 9-14个
```

### 2. Playwright工具优化
如果您不需要浏览器自动化功能，可以：
- 完全禁用 `playwright-official` (减少24个工具)
- 或者只保留核心功能：
  - `browser_snapshot`: 页面分析
  - `browser_evaluate`: JavaScript执行
  - `browser_screenshot`: 截图功能

### 3. Memory工具优化
根据使用频率选择性启用：
- **高频使用**: 保留所有9个工具
- **中频使用**: 保留核心工具（创建、查询、删除）
- **低频使用**: 只保留查询工具

### 4. 推荐配置
```json
{
  "mcpServers": {
    "sequential-thinking-mcp": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "toolFilter": {
        "include": [
          "mcp_memory_create_entities",
          "mcp_memory_search_nodes",
          "mcp_memory_read_graph"
        ]
      }
    },
    "playwright-official": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-playwright"],
      "toolFilter": {
        "include": [
          "browser_snapshot",
          "browser_evaluate",
          "browser_screenshot"
        ]
      }
    }
  }
}
```

## 🔍 如何查看具体工具

### 1. 在Cursor中查看
1. 打开MCP设置
2. 点击MCP服务器名称
3. 查看工具列表和功能说明

### 2. 通过命令行查看
```bash
# 查看playwright工具
npx @modelcontextprotocol/server-playwright --help

# 查看memory工具
npx @modelcontextprotocol/server-memory --help
```

### 3. 查看文档
每个MCP服务器都有详细的工具文档，说明每个工具的功能和参数。

## 💡 实用建议

### 1. 渐进式优化
- 先禁用最不常用的MCP服务器
- 然后逐步优化每个服务器内的工具
- 定期评估工具使用情况

### 2. 项目导向
- 根据当前项目需求选择工具
- 不同项目可以有不同的工具配置
- 保存多个配置文件用于不同场景

### 3. 性能监控
- 监控Cursor的响应速度
- 观察内存使用情况
- 根据性能表现调整工具配置

## 🎉 总结

通过单独管理MCP工具，您可以：
- ✅ 精确控制工具数量
- ✅ 提高Cursor性能
- ✅ 减少资源占用
- ✅ 保持核心功能

建议您先尝试禁用 `playwright-official` 的某些工具，这样可以立即减少10-15个工具，显著提升性能！ 