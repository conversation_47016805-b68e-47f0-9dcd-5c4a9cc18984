# Hugging Face MCP 配置指南

## 概述
Hugging Face MCP 允许您直接在 Claude Desktop 中访问和使用 Hugging Face 的模型和数据集。

## 安装步骤

### 1. 安装 Hugging Face MCP 包
```bash
npm install -g @modelcontextprotocol/server-huggingface
```

### 2. 获取 Hugging Face API Token
1. 访问 https://huggingface.co/settings/tokens
2. 创建新的 API Token
3. 复制 Token 备用

### 3. 配置 Claude Desktop

在 Claude Desktop 的配置文件中添加以下配置：

```json
{
  "mcpServers": {
    "huggingface-mcp": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-huggingface"],
      "env": {
        "HUGGINGFACE_API_KEY": "your_huggingface_api_token_here"
      }
    }
  }
}
```

## 功能特性

### 模型管理
- 搜索和浏览模型
- 下载模型到本地
- 查看模型详情和元数据
- 管理模型版本

### 数据集管理
- 搜索和浏览数据集
- 下载数据集
- 查看数据集详情
- 数据集版本管理

### 推理服务
- 本地模型推理
- 云端模型调用
- 批量推理处理
- 自定义推理参数

## 常用命令示例

### 搜索模型
```
搜索文本分类模型
```

### 下载模型
```
下载 bert-base-uncased 模型到本地
```

### 数据集操作
```
搜索中文文本数据集
```

## 注意事项

1. **API 限制**: 注意 Hugging Face API 的调用限制
2. **存储空间**: 下载模型需要足够的本地存储空间
3. **网络连接**: 确保网络连接稳定，特别是下载大型模型时
4. **权限管理**: 合理管理 API Token 的权限范围

## 故障排除

### 常见问题
1. **API Token 无效**: 检查 Token 是否正确设置
2. **网络连接问题**: 检查网络连接和防火墙设置
3. **存储空间不足**: 清理本地存储空间
4. **模型下载失败**: 重试下载或检查网络连接

### 调试命令
```bash
# 检查 MCP 服务器状态
npx @modelcontextprotocol/server-huggingface --help

# 测试 API 连接
curl -H "Authorization: Bearer YOUR_TOKEN" https://huggingface.co/api/models
```

## 更新和维护

### 更新 MCP 包
```bash
npm update -g @modelcontextprotocol/server-huggingface
```

### 检查版本
```bash
npx @modelcontextprotocol/server-huggingface --version
```

## 安全建议

1. **Token 安全**: 不要在代码中硬编码 API Token
2. **权限最小化**: 只授予必要的权限
3. **定期轮换**: 定期更新 API Token
4. **监控使用**: 监控 API 使用情况

## 相关资源

- [Hugging Face MCP 官方文档](https://github.com/modelcontextprotocol/server-huggingface)
- [Hugging Face API 文档](https://huggingface.co/docs/api-inference)
- [MCP 协议规范](https://modelcontextprotocol.io/) 