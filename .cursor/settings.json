{"markdown.preview.openMarkdownLinks": "inPreview", "markdown.preview.doubleClickToSwitchToEditor": true, "workbench.editor.enablePreview": false, "markdown.preview.openPreviewWithEditor": false, "markdown.preview.scrollPreviewWithEditor": true, "markdown.preview.scrollEditorWithPreview": true, "markdown.preview.fontSize": 16, "markdown.preview.lineHeight": 1.8, "markdown.preview.fontFamily": "-apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif", "markdown.preview.codeFontFamily": "'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace", "markdown.preview.breaks": true, "markdown.preview.linkify": true, "markdown.preview.marker": true, "markdown.preview.scrollBeyondLastLine": false, "markdown.preview.wordWrap": "on", "editor.wordWrap": "on", "editor.renderWhitespace": "none", "editor.minimap.enabled": false, "editor.lineNumbers": "off", "editor.folding": false, "editor.rulers": [], "editor.glyphMargin": false, "editor.lineDecorationsWidth": 0, "editor.lineNumbersMinChars": 0, "editor.scrollBeyondLastLine": false, "editor.cursorBlinking": "smooth", "editor.cursorSmoothCaretAnimation": "on", "editor.fontSize": 16, "editor.fontFamily": "-apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif", "editor.lineHeight": 1.8, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000}