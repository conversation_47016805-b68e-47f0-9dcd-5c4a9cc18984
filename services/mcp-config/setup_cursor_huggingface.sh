#!/bin/bash

# Cursor Hugging Face MCP 配置脚本
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

set -e

echo "🚀 Cursor Hugging Face MCP 配置脚本"
echo "===================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查系统环境
echo ""
print_info "检查系统环境..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js 未安装"
    print_info "请先安装 Node.js: https://nodejs.org/"
    exit 1
fi
print_status "Node.js 已安装: $(node --version)"

# 检查 npm
if ! command -v npm &> /dev/null; then
    print_error "npm 未安装"
    exit 1
fi
print_status "npm 已安装: $(npm --version)"

# 安装 Hugging Face MCP 包
echo ""
print_info "安装 Hugging Face MCP 包..."
if npm install -g huggingface-mcp-server; then
    print_status "Hugging Face MCP 包安装成功"
else
    print_error "Hugging Face MCP 包安装失败"
    exit 1
fi

# 验证安装
echo ""
print_info "验证安装..."
if npx huggingface-mcp-server --help &> /dev/null; then
    print_status "Hugging Face MCP 安装验证成功"
else
    print_error "Hugging Face MCP 安装验证失败"
    exit 1
fi

# 创建 Cursor MCP 配置目录
echo ""
print_info "创建 Cursor MCP 配置..."

CURSOR_CONFIG_DIR=".cursor"
MCP_CONFIG_FILE="$CURSOR_CONFIG_DIR/mcp.json"

# 创建 .cursor 目录（如果不存在）
mkdir -p "$CURSOR_CONFIG_DIR"

# 检查是否已存在配置文件
if [ -f "$MCP_CONFIG_FILE" ]; then
    print_warning "Cursor MCP 配置文件已存在，将备份原文件"
    cp "$MCP_CONFIG_FILE" "${MCP_CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 创建新的 MCP 配置文件
cat > "$MCP_CONFIG_FILE" << 'EOF'
{
  "mcpServers": {
    "huggingface-mcp": {
      "command": "npx",
      "args": ["-y", "huggingface-mcp-server"],
      "env": {
        "HUGGINGFACE_API_KEY": "hf_your_token_here"
      }
    },
    "firecrawl-mcp": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "fc-0a2c801f433d4718bcd8189f2742edf4"
      }
    },
    "sequential-thinking-mcp": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    }
  }
}
EOF

print_status "Cursor MCP 配置文件已创建: $MCP_CONFIG_FILE"

# 创建 API Token 配置指南
echo ""
print_info "创建 API Token 配置指南..."

cat > cursor_huggingface_guide.md << 'EOF'
# Cursor Hugging Face MCP 配置指南

## 配置步骤

### 1. 获取 Hugging Face API Token
1. 访问: https://huggingface.co/settings/tokens
2. 登录您的账户
3. 点击 "New token"
4. 输入名称（如: "Cursor MCP"）
5. 选择权限（建议: Read）
6. 点击 "Generate token"
7. 复制生成的 Token（以 "hf_" 开头）

### 2. 更新配置文件
1. 打开文件: `.cursor/mcp.json`
2. 找到 "huggingface-mcp" 配置项
3. 将 "hf_your_token_here" 替换为您的真实 Token
4. 保存文件

### 3. 重启 Cursor
1. 完全关闭 Cursor
2. 重新启动 Cursor
3. 检查 MCP 工具是否可用

## 功能特性

### 模型管理
- 搜索和浏览 Hugging Face 模型
- 下载模型到本地
- 查看模型详情和元数据

### 数据集管理
- 搜索和浏览数据集
- 下载数据集
- 查看数据集详情

### 推理服务
- 本地模型推理
- 云端模型调用
- 批量推理处理

## 使用示例

在 Cursor 中，您可以：
- 搜索模型: "搜索中文文本分类模型"
- 下载模型: "下载 bert-base-chinese 模型"
- 查看数据集: "搜索中文新闻数据集"

## 注意事项

1. **工具数量限制**: 当前您有54个工具，建议控制在40个以内
2. **API 限制**: 注意 Hugging Face API 的调用限制
3. **存储空间**: 下载模型需要足够的本地存储空间
4. **网络连接**: 确保网络连接稳定

## 故障排除

### 常见问题
1. **Token 无效**: 检查 Token 是否正确设置
2. **工具不显示**: 重启 Cursor 并检查配置
3. **网络连接问题**: 检查网络连接和防火墙设置

### 调试命令
```bash
# 测试 MCP 服务器
npx huggingface-mcp-server --help

# 测试 API 连接
curl -H "Authorization: Bearer YOUR_TOKEN" https://huggingface.co/api/models
```

## 安全建议

1. **Token 安全**: 不要在代码中硬编码 API Token
2. **权限最小化**: 只授予必要的权限
3. **定期轮换**: 定期更新 API Token
4. **监控使用**: 监控 API 使用情况
EOF

print_status "配置指南已创建: cursor_huggingface_guide.md"

# 创建测试脚本
echo ""
print_info "创建 Cursor 专用测试脚本..."

cat > test_cursor_huggingface.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Hugging Face MCP 测试脚本
"""

import os
import json
import subprocess
from pathlib import Path

def test_cursor_huggingface_mcp():
    """测试 Cursor Hugging Face MCP 配置"""
    
    print("🧪 测试 Cursor Hugging Face MCP 配置...")
    
    # 检查配置文件
    config_file = Path(".cursor/mcp.json")
    if not config_file.exists():
        print("❌ Cursor MCP 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" in config and "huggingface-mcp" in config["mcpServers"]:
            print("✅ Hugging Face MCP 配置已添加到 Cursor 配置文件中")
            
            # 检查 API Token
            huggingface_config = config["mcpServers"]["huggingface-mcp"]
            if "env" in huggingface_config and "HUGGINGFACE_API_KEY" in huggingface_config["env"]:
                api_key = huggingface_config["env"]["HUGGINGFACE_API_KEY"]
                if api_key != "hf_your_token_here":
                    print("✅ Hugging Face API Token 已配置")
                else:
                    print("⚠️  警告: 请将 Hugging Face API Token 替换为真实值")
            else:
                print("❌ 错误: Hugging Face API Token 未配置")
                return False
        else:
            print("❌ 错误: Hugging Face MCP 配置未找到")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件读取错误: {e}")
        return False
    
    # 检查 MCP 包安装
    try:
        result = subprocess.run(
            ["npx", "huggingface-mcp-server", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print("✅ Hugging Face MCP 包已正确安装")
        else:
            print("❌ Hugging Face MCP 包检查失败")
            return False
    except Exception as e:
        print(f"❌ MCP 包检查异常: {e}")
        return False
    
    print("\n🎉 Cursor Hugging Face MCP 配置测试完成！")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("Cursor Hugging Face MCP 配置测试")
    print("=" * 50)
    
    success = test_cursor_huggingface_mcp()
    
    if success:
        print("\n🎯 下一步操作:")
        print("1. 更新 API Token 配置")
        print("2. 重启 Cursor")
        print("3. 在 Cursor 中测试 Hugging Face MCP 功能")
    else:
        print("\n❌ 配置测试失败，请检查上述错误信息")
    
    print("=" * 50)
EOF

chmod +x test_cursor_huggingface.py
print_status "测试脚本已创建: test_cursor_huggingface.py"

# 显示完成信息
echo ""
echo "🎉 Cursor Hugging Face MCP 配置完成！"
echo ""
echo "📋 配置总结:"
print_status "✅ Hugging Face MCP 包已安装"
print_status "✅ Cursor MCP 配置文件已创建"
print_status "✅ 配置指南已生成"
print_status "✅ 测试脚本已创建"
echo ""
echo "🎯 下一步操作:"
echo "1. 获取 Hugging Face API Token（参考: cursor_huggingface_guide.md）"
echo "2. 更新 .cursor/mcp.json 中的 Token"
echo "3. 重启 Cursor"
echo "4. 运行测试: python3 test_cursor_huggingface.py"
echo ""
echo "📚 相关文档:"
echo "- 配置指南: cursor_huggingface_guide.md"
echo "- 测试脚本: test_cursor_huggingface.py"
echo "- MCP 配置文件: .cursor/mcp.json"
echo ""
echo "⚠️  重要提醒:"
echo "- 当前您有54个工具，建议控制在40个以内以保持性能"
echo "- 可以在 Cursor 设置中禁用不需要的 MCP 工具" 