<?php
// 引入API处理器
require_once 'api/handlers/handlers.class.php';

// 发布单条新闻
function publishSingleNews($newsData) {
    $handels = new handlers('article', 'put');
    
    $param = [
        'title' => $newsData['title'],
        'typeid' => $newsData['typeid'], // 根据AI分类结果设置
        'body' => $newsData['content'],
        'writer' => 'AI采集系统',
        'source' => $newsData['source_url'],
        'keywords' => $newsData['keywords'],
        'description' => $newsData['description'],
        'vdimgck' => 'auto' // 可能需要管理员权限绕过验证码
    ];
    
    $result = $handels->getHandle($param);
    
    if ($result['state'] == 100) {
        echo "新闻发布成功: " . $newsData['title'];
        return true;
    } else {
        echo "发布失败: " . $result['info'];
        return false;
    }
}

// 使用示例
$newsData = [
    'title' => '夏威夷华人社区最新活动通知',
    'typeid' => 2, // 社区新闻分类
    'content' => '<p>这里是新闻内容...</p>',
    'source_url' => 'https://hawaiinewsnow.com',
    'keywords' => '夏威夷,华人,社区',
    'description' => '夏威夷华人社区最新活动信息'
];

publishSingleNews($newsData);
?>