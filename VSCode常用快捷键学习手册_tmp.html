<!DOCTYPE html>
<html>
<head>
<title>VSCode常用快捷键学习手册.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="vscode-%E5%B8%B8%E7%94%A8%E5%BF%AB%E6%8D%B7%E9%94%AE%E5%AD%A6%E4%B9%A0%E6%89%8B%E5%86%8C-%F0%9F%93%9A">VSCode 常用快捷键学习手册 📚</h1>
<blockquote>
<p><strong>适用系统</strong>: macOS<br>
<strong>更新时间</strong>: 2025年1月<br>
<strong>难度等级</strong>: 入门到进阶</p>
</blockquote>
<hr>
<h2 id="%F0%9F%9A%80-%E5%9F%BA%E7%A1%80%E7%BC%96%E8%BE%91%E5%BF%AB%E6%8D%B7%E9%94%AE%E5%BF%85%E4%BC%9A">🚀 基础编辑快捷键（必会）</h2>
<h3 id="%E6%96%87%E6%9C%AC%E9%80%89%E6%8B%A9">文本选择</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+A</code></td>
<td>全选</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+L</code></td>
<td>选择整行</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+D</code></td>
<td>选择当前单词（多次按选择相同单词）</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+L</code></td>
<td>选择所有相同单词</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Option+Shift+鼠标拖拽</code></td>
<td>多光标选择</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E6%96%87%E6%9C%AC%E6%93%8D%E4%BD%9C">文本操作</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+C/V/X</code></td>
<td>复制/粘贴/剪切</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Z/Shift+Cmd+Z</code></td>
<td>撤销/重做</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+/</code></td>
<td>注释/取消注释</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Option+Shift+A</code></td>
<td>块注释</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+]</code> / <code>Cmd+[</code></td>
<td>增加/减少缩进</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E8%A1%8C%E6%93%8D%E4%BD%9C">行操作</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Option+↑/↓</code></td>
<td>移动当前行</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Option+Shift+↑/↓</code></td>
<td>复制当前行</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+K</code></td>
<td>删除当前行</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Enter</code></td>
<td>在下方插入新行</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+Enter</code></td>
<td>在上方插入新行</td>
<td>⭐⭐⭐</td>
</tr>
</tbody>
</table>
<hr>
<h2 id="%F0%9F%93%81-%E6%96%87%E4%BB%B6%E5%92%8C%E7%AA%97%E5%8F%A3%E6%93%8D%E4%BD%9C">📁 文件和窗口操作</h2>
<h3 id="%E6%96%87%E4%BB%B6%E7%AE%A1%E7%90%86">文件管理</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+N</code></td>
<td>新建文件</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+O</code></td>
<td>打开文件</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+S</code></td>
<td>保存文件</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+S</code></td>
<td>另存为</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+W</code></td>
<td>关闭当前标签</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+T</code></td>
<td>重新打开关闭的标签</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E6%A0%87%E7%AD%BE%E9%A1%B5%E5%88%87%E6%8D%A2">标签页切换</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+Tab</code></td>
<td>切换应用程序</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Ctrl+Tab</code></td>
<td>切换VSCode标签页</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+1/2/3...</code></td>
<td>切换到第N个标签页</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Option+←/→</code></td>
<td>切换标签页</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E7%AA%97%E5%8F%A3%E5%88%86%E5%89%B2">窗口分割</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+\</code></td>
<td>垂直分割编辑器</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+K Cmd+\</code></td>
<td>水平分割编辑器</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+1/2/3</code></td>
<td>切换到第N个编辑器组</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+K Cmd+←/→</code></td>
<td>移动标签到其他编辑器组</td>
<td>⭐⭐⭐</td>
</tr>
</tbody>
</table>
<hr>
<h2 id="%F0%9F%94%8D-%E6%90%9C%E7%B4%A2%E5%92%8C%E5%AF%BC%E8%88%AA">🔍 搜索和导航</h2>
<h3 id="%E6%90%9C%E7%B4%A2%E5%8A%9F%E8%83%BD">搜索功能</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+F</code></td>
<td>在当前文件中搜索</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+F</code></td>
<td>在整个项目中搜索</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+H</code></td>
<td>替换</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+H</code></td>
<td>在整个项目中替换</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+G</code> / <code>Cmd+Shift+G</code></td>
<td>查找下一个/上一个</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E5%BF%AB%E9%80%9F%E5%AF%BC%E8%88%AA">快速导航</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+P</code></td>
<td>快速打开文件</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+P</code></td>
<td>命令面板</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+G</code></td>
<td>跳转到指定行</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+O</code></td>
<td>跳转到符号（函数、变量等）</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+T</code></td>
<td>在工作区中搜索符号</td>
<td>⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E4%BB%A3%E7%A0%81%E5%AF%BC%E8%88%AA">代码导航</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>F12</code></td>
<td>跳转到定义</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+F12</code></td>
<td>跳转到实现</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Shift+F12</code></td>
<td>查找所有引用</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+←/→</code></td>
<td>跳转到行首/行尾</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+↑/↓</code></td>
<td>跳转到文件开头/结尾</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<hr>
<h2 id="%F0%9F%90%9B-%E8%B0%83%E8%AF%95%E5%92%8C%E7%BB%88%E7%AB%AF">🐛 调试和终端</h2>
<h3 id="%E8%B0%83%E8%AF%95%E5%BF%AB%E6%8D%B7%E9%94%AE">调试快捷键</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>F5</code></td>
<td>开始调试</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>F9</code></td>
<td>切换断点</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>F10</code></td>
<td>单步跳过</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>F11</code></td>
<td>单步进入</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Shift+F11</code></td>
<td>单步跳出</td>
<td>⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E7%BB%88%E7%AB%AF%E6%93%8D%E4%BD%9C">终端操作</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Ctrl+`` </code></td>
<td>打开/关闭终端</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+`` </code></td>
<td>新建终端</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+\</code></td>
<td>分割终端</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+K</code></td>
<td>清空终端</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<hr>
<h2 id="%F0%9F%8E%A8-%E7%95%8C%E9%9D%A2%E5%92%8C%E8%A7%86%E5%9B%BE">🎨 界面和视图</h2>
<h3 id="%E4%BE%A7%E8%BE%B9%E6%A0%8F%E6%8E%A7%E5%88%B6">侧边栏控制</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+B</code></td>
<td>切换侧边栏显示</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+E</code></td>
<td>显示文件资源管理器</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+F</code></td>
<td>显示搜索面板</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+G</code></td>
<td>显示Git面板</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+D</code></td>
<td>显示调试面板</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+X</code></td>
<td>显示扩展面板</td>
<td>⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E6%98%BE%E7%A4%BA%E6%8E%A7%E5%88%B6">显示控制</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Cmd+J</code></td>
<td>切换面板显示</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+Y</code></td>
<td>显示调试控制台</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+U</code></td>
<td>显示输出面板</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Shift+M</code></td>
<td>显示问题面板</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<hr>
<h2 id="%F0%9F%94%A7-%E4%BB%A3%E7%A0%81%E7%BC%96%E8%BE%91%E8%BF%9B%E9%98%B6">🔧 代码编辑进阶</h2>
<h3 id="%E5%A4%9A%E5%85%89%E6%A0%87%E7%BC%96%E8%BE%91">多光标编辑</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Option+Click</code></td>
<td>添加光标</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+Option+↑/↓</code></td>
<td>在上方/下方添加光标</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+U</code></td>
<td>撤销上一个光标操作</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td><code>Escape</code></td>
<td>取消多光标</td>
<td>⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<h3 id="%E4%BB%A3%E7%A0%81%E6%A0%BC%E5%BC%8F%E5%8C%96">代码格式化</h3>
<table>
<thead>
<tr>
<th>快捷键</th>
<th>功能</th>
<th>使用频率</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Option+Shift+F</code></td>
<td>格式化整个文档</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+K Cmd+F</code></td>
<td>格式化选中代码</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Ctrl+Space</code></td>
<td>触发建议</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td><code>Cmd+.</code></td>
<td>快速修复</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
</tbody>
</table>
<hr>
<h2 id="%F0%9F%93%8B-%E5%AE%9E%E7%94%A8%E6%8A%80%E5%B7%A7%E7%BB%84%E5%90%88">📋 实用技巧组合</h2>
<h3 id="%F0%9F%94%A5-%E8%B6%85%E5%AE%9E%E7%94%A8%E7%BB%84%E5%90%88%E6%8A%80">🔥 超实用组合技</h3>
<ol>
<li><strong>快速重构变量名</strong>: <code>F2</code> → 输入新名称 → <code>Enter</code></li>
<li><strong>快速复制行并修改</strong>: <code>Option+Shift+↓</code> → 修改内容</li>
<li><strong>快速选择相同内容</strong>: <code>Cmd+D</code> → <code>Cmd+D</code> → <code>Cmd+D</code>...</li>
<li><strong>快速打开文件</strong>: <code>Cmd+P</code> → 输入文件名 → <code>Enter</code></li>
<li><strong>快速执行命令</strong>: <code>Cmd+Shift+P</code> → 输入命令 → <code>Enter</code></li>
</ol>
<h3 id="%F0%9F%92%A1-%E6%95%88%E7%8E%87%E6%8F%90%E5%8D%87%E6%8A%80%E5%B7%A7">💡 效率提升技巧</h3>
<ul>
<li><strong>Emmet缩写</strong>: 输入 <code>div.container</code> → <code>Tab</code> 生成HTML</li>
<li><strong>代码片段</strong>: 输入 <code>log</code> → <code>Tab</code> 生成console.log</li>
<li><strong>智能感知</strong>: <code>Ctrl+Space</code> 随时触发代码提示</li>
<li><strong>面包屑导航</strong>: <code>Cmd+Shift+.</code> 显示当前位置</li>
</ul>
<hr>
<h2 id="%F0%9F%8E%AF-%E5%AD%A6%E4%B9%A0%E5%BB%BA%E8%AE%AE">🎯 学习建议</h2>
<h3 id="%F0%9F%93%88-%E5%AD%A6%E4%B9%A0%E4%BC%98%E5%85%88%E7%BA%A7">📈 学习优先级</h3>
<ol>
<li><strong>第一周</strong>: 掌握基础编辑（复制粘贴、撤销重做、注释）</li>
<li><strong>第二周</strong>: 学会文件操作和搜索功能</li>
<li><strong>第三周</strong>: 掌握代码导航和多光标编辑</li>
<li><strong>第四周</strong>: 学习调试和终端操作</li>
</ol>
<h3 id="%F0%9F%8F%83%E2%80%8D%E2%99%82%EF%B8%8F-%E7%BB%83%E4%B9%A0%E6%96%B9%E6%B3%95">🏃‍♂️ 练习方法</h3>
<ul>
<li><strong>每天练习</strong>: 选择5个快捷键重复使用</li>
<li><strong>刻意练习</strong>: 强迫自己用快捷键而不是鼠标</li>
<li><strong>记忆卡片</strong>: 制作快捷键记忆卡片</li>
<li><strong>实际项目</strong>: 在真实项目中应用学到的快捷键</li>
</ul>
<h3 id="%F0%9F%93%9D-%E8%87%AA%E5%AE%9A%E4%B9%89%E5%BF%AB%E6%8D%B7%E9%94%AE">📝 自定义快捷键</h3>
<ul>
<li>打开设置: <code>Cmd+,</code> → 搜索 &quot;keyboard shortcuts&quot;</li>
<li>或直接: <code>Cmd+K Cmd+S</code> 打开快捷键设置</li>
<li>可以根据个人习惯自定义快捷键</li>
</ul>
<hr>
<h2 id="%F0%9F%8E%89-%E6%80%BB%E7%BB%93">🎉 总结</h2>
<p>掌握这些快捷键后，你的编码效率将提升**300%**以上！</p>
<p><strong>记住</strong>:</p>
<ul>
<li>🎯 <strong>不要贪多</strong> - 每周学5-10个就够了</li>
<li>🔄 <strong>重复练习</strong> - 肌肉记忆需要时间</li>
<li>💪 <strong>坚持使用</strong> - 强迫自己用快捷键</li>
<li>🚀 <strong>享受过程</strong> - 感受效率提升的快感</li>
</ul>
<hr>
<p><em>Happy Coding! 🚀</em></p>

</body>
</html>
